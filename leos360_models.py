#!/usr/bin/env python3.12
"""
LEOS360 Platform - Pydantic Data Models
=======================================

This module contains Pydantic models for structured data validation
and type safety across the LEOS360 platform. These models replace
manual regex validation with modern, type-safe validation patterns.

Author: LEOS360 Development Team
Version: 3.0
Last Updated: 2025-01-02

Features:
- Customer configuration validation
- IP address and network validation
- DNS name validation with proper formatting
- URL validation with scheme checking
- Automatic type conversion and validation
- Detailed error messages for validation failures
"""

import re
import ipaddress
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse

# Modern validation with Pydantic (fallback to manual validation if not available)
try:
    from pydantic import BaseModel, Field, validator, root_validator
    from pydantic.networks import IPvAnyAddress
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    # Fallback base class for when Pydantic is not available
    class BaseModel:
        pass

# =============================================================================
# VALIDATION PATTERNS (for fallback compatibility)
# =============================================================================

CUSTOMER_NAME_PATTERN = r'^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$'
DNS_NAME_PATTERN = r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.?$'
CUSTOMER_IP_PATTERN = r'^172\.16\.7\.\d{1,3}$'

# =============================================================================
# PYDANTIC MODELS (Modern Validation)
# =============================================================================

if PYDANTIC_AVAILABLE:
    
    class CustomerConfig(BaseModel):
        """
        Customer configuration model with comprehensive validation.
        
        This model validates all customer-related configuration parameters
        including names, IP addresses, domains, and service configurations.
        """
        customer_name: str = Field(
            ..., 
            regex=CUSTOMER_NAME_PATTERN,
            description="Customer name (lowercase alphanumeric with hyphens/underscores)"
        )
        customer_ip: str = Field(
            ..., 
            regex=CUSTOMER_IP_PATTERN,
            description="Customer IP address in 172.16.7.x range"
        )
        customer_domain: str = Field(
            ..., 
            regex=DNS_NAME_PATTERN,
            description="Customer domain name"
        )
        
        @validator('customer_name')
        def validate_customer_name_length(cls, v):
            if len(v) < 1 or len(v) > 63:
                raise ValueError('Customer name must be between 1 and 63 characters')
            return v
        
        @validator('customer_ip')
        def validate_customer_ip_range(cls, v):
            try:
                ip = ipaddress.IPv4Address(v)
                network = ipaddress.IPv4Network('**********/24')
                if ip not in network:
                    raise ValueError('Customer IP must be in **********/24 range')
                # Reserve .1 for gateway, .254 for broadcast-like usage
                if ip.packed[-1] in [1, 254, 255]:
                    raise ValueError('IP addresses .1, .254, and .255 are reserved')
                return v
            except ipaddress.AddressValueError:
                raise ValueError('Invalid IP address format')
        
        @validator('customer_domain')
        def validate_domain_format(cls, v):
            if len(v) > 253:
                raise ValueError('Domain name too long (max 253 characters)')
            if '..' in v:
                raise ValueError('Domain name cannot contain consecutive dots')
            return v.lower()
        
        class Config:
            # Enable validation on assignment
            validate_assignment = True
            # Use enum values for serialization
            use_enum_values = True
            # Additional validation
            anystr_strip_whitespace = True
    
    
    class DatabaseConfig(BaseModel):
        """Database configuration model with connection validation."""
        
        db_name: str = Field(..., min_length=1, max_length=63)
        db_user: str = Field(..., min_length=1, max_length=63)
        db_password: str = Field(..., min_length=8)
        db_host: str = Field(default="************")
        db_port: int = Field(default=5432, ge=1, le=65535)
        
        @validator('db_name', 'db_user')
        def validate_db_identifiers(cls, v):
            # PostgreSQL identifier rules
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', v):
                raise ValueError('Database identifiers must start with letter/underscore and contain only alphanumeric/underscore')
            return v
        
        @validator('db_password')
        def validate_password_strength(cls, v):
            if len(v) < 8:
                raise ValueError('Password must be at least 8 characters long')
            return v
    
    
    class ServiceConfig(BaseModel):
        """Service configuration model for Docker services."""
        
        service_name: str = Field(..., min_length=1, max_length=100)
        image: str = Field(..., min_length=1)
        ports: Optional[List[str]] = Field(default_factory=list)
        environment: Optional[Dict[str, str]] = Field(default_factory=dict)
        volumes: Optional[List[str]] = Field(default_factory=list)
        
        @validator('service_name')
        def validate_service_name(cls, v):
            # Docker service name rules
            if not re.match(r'^[a-zA-Z0-9][a-zA-Z0-9_.-]*$', v):
                raise ValueError('Service name must start with alphanumeric and contain only alphanumeric, underscore, dot, hyphen')
            return v
        
        @validator('ports')
        def validate_ports(cls, v):
            if v:
                for port in v:
                    if ':' in port:
                        host_port, container_port = port.split(':', 1)
                        try:
                            host_p = int(host_port)
                            container_p = int(container_port)
                            if not (1 <= host_p <= 65535) or not (1 <= container_p <= 65535):
                                raise ValueError(f'Invalid port range in {port}')
                        except ValueError:
                            raise ValueError(f'Invalid port format: {port}')
            return v
    
    
    class TenantSetupConfig(BaseModel):
        """Complete tenant setup configuration model."""
        
        customer: CustomerConfig
        database: DatabaseConfig
        services: List[ServiceConfig] = Field(default_factory=list)
        
        # Additional tenant-specific settings
        enable_ssl: bool = Field(default=True)
        backup_enabled: bool = Field(default=True)
        monitoring_enabled: bool = Field(default=True)
        
        class Config:
            validate_assignment = True
            use_enum_values = True

else:
    # Fallback classes when Pydantic is not available
    class CustomerConfig:
        """Fallback customer configuration without Pydantic validation."""
        def __init__(self, customer_name: str, customer_ip: str, customer_domain: str):
            self.customer_name = customer_name
            self.customer_ip = customer_ip
            self.customer_domain = customer_domain
    
    class DatabaseConfig:
        """Fallback database configuration without Pydantic validation."""
        def __init__(self, db_name: str, db_user: str, db_password: str, 
                     db_host: str = "************", db_port: int = 5432):
            self.db_name = db_name
            self.db_user = db_user
            self.db_password = db_password
            self.db_host = db_host
            self.db_port = db_port
    
    class ServiceConfig:
        """Fallback service configuration without Pydantic validation."""
        def __init__(self, service_name: str, image: str, ports=None, environment=None, volumes=None):
            self.service_name = service_name
            self.image = image
            self.ports = ports or []
            self.environment = environment or {}
            self.volumes = volumes or []
    
    class TenantSetupConfig:
        """Fallback tenant setup configuration without Pydantic validation."""
        def __init__(self, customer: CustomerConfig, database: DatabaseConfig, services=None):
            self.customer = customer
            self.database = database
            self.services = services or []
            self.enable_ssl = True
            self.backup_enabled = True
            self.monitoring_enabled = True

# =============================================================================
# VALIDATION FUNCTIONS (Compatibility Layer)
# =============================================================================

def validate_customer_config(customer_name: str, customer_ip: str, customer_domain: str) -> bool:
    """
    Validate customer configuration using Pydantic if available, otherwise fallback.
    
    Args:
        customer_name: Customer name to validate
        customer_ip: Customer IP address to validate
        customer_domain: Customer domain to validate
    
    Returns:
        True if valid, raises ValidationError if invalid
    """
    if PYDANTIC_AVAILABLE:
        try:
            CustomerConfig(
                customer_name=customer_name,
                customer_ip=customer_ip,
                customer_domain=customer_domain
            )
            return True
        except Exception as e:
            raise ValueError(f"Customer configuration validation failed: {e}")
    else:
        # Fallback to manual validation
        if not re.match(CUSTOMER_NAME_PATTERN, customer_name):
            raise ValueError("Invalid customer name format")
        if not re.match(CUSTOMER_IP_PATTERN, customer_ip):
            raise ValueError("Invalid customer IP format")
        if not re.match(DNS_NAME_PATTERN, customer_domain):
            raise ValueError("Invalid customer domain format")
        return True


def create_customer_config(customer_name: str, customer_ip: str, customer_domain: str) -> CustomerConfig:
    """
    Create and validate customer configuration.
    
    Args:
        customer_name: Customer name
        customer_ip: Customer IP address
        customer_domain: Customer domain
    
    Returns:
        CustomerConfig instance (Pydantic model or fallback class)
    """
    if PYDANTIC_AVAILABLE:
        return CustomerConfig(
            customer_name=customer_name,
            customer_ip=customer_ip,
            customer_domain=customer_domain
        )
    else:
        # Validate manually before creating fallback instance
        validate_customer_config(customer_name, customer_ip, customer_domain)
        return CustomerConfig(customer_name, customer_ip, customer_domain)
