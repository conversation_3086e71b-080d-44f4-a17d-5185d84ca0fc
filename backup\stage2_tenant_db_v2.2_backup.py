#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 2: Database Setup
==========================================

This script handles the database setup for a new tenant in the LEOS360 platform.
It creates PostgreSQL databases, users, and permissions for all required services
including Nextcloud, Keycloak, and LLDAP.

Author: LEOS360 Development Team
Version: 2.2
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 must be completed successfully
- PostgreSQL server must be accessible at ************
- Database admin credentials must be available
- SQL setup file must exist in tenant directory

Usage:
    python3 stage2_tenant_db.py <customer_name> [options]

Examples:
    python3 stage2_tenant_db.py example-customer
    python3 stage2_tenant_db.py example-customer --status
    python3 stage2_tenant_db.py example-customer --reset --force
"""

import os
import sys
import subprocess
import tempfile
import argparse
import time
import re
import logging
import contextlib
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
import psycopg2
from psycopg2 import sql, OperationalError, DatabaseError
import dotenv

# =============================================================================
# CUSTOM EXCEPTIONS
# =============================================================================

class DatabaseSetupError(Exception):
    """Base exception for database setup errors."""
    pass

class ConnectionError(DatabaseSetupError):
    """Raised when database connection fails."""
    pass

class ConfigurationError(DatabaseSetupError):
    """Raised when configuration is invalid."""
    pass

class DatabaseExistsError(DatabaseSetupError):
    """Raised when databases already exist."""
    pass

class ValidationError(DatabaseSetupError):
    """Raised when validation fails."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.2"
SCRIPT_NAME = "Stage 2: Database Setup"

# Database connection details
DB_HOST = "************"
DB_PORT = 5432
DB_ADMIN_USER = "postgres"
DB_TEMPLATE = "template1"

# Connection settings
DB_CONNECT_TIMEOUT = 30
DB_COMMAND_TIMEOUT = 300
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY = 2

# File paths and patterns
BASE_TENANT_PATH = Path("/mnt/storage/tenants")
SQL_FILE_PATTERN = "{base_path}/{customer_name}/db/db_setup_{customer_name}.sql"
ENV_FILE_PATTERN = "{base_path}/{customer_name}/.env"
LOG_FILE_PATTERN = "{base_path}/{customer_name}/logs/db_setup.log"

# Database and user naming patterns
DB_NEXTCLOUD = "{customer_name}_nextcloud"
DB_KEYCLOAK = "{customer_name}_keycloak"
DB_LLDAP = "{customer_name}_lldap"
DB_USER = "{customer_name}_admin"

# Environment variable names
ENV_VAR_DB_PASSWORD = "DB_ADMIN_PASSWORD"

# File permissions
PGPASS_PERMISSIONS = 0o600
LOG_PERMISSIONS = 0o644

# Validation patterns
CUSTOMER_NAME_PATTERN = r'^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$'


# =============================================================================
# LOGGING SETUP
# =============================================================================

def setup_logging(customer_name: str) -> logging.Logger:
    """Setup logging configuration."""
    logger = logging.getLogger('db_setup')
    logger.setLevel(logging.INFO)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    try:
        log_file = LOG_FILE_PATTERN.format(
            base_path=BASE_TENANT_PATH,
            customer_name=customer_name
        )
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Set log file permissions
        os.chmod(log_file, LOG_PERMISSIONS)
    except (OSError, PermissionError) as e:
        print(f"[WARNING] Could not setup file logging: {e}")
    
    return logger


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def validate_customer_name(customer_name: str) -> bool:
    """
    Validate customer name format.
    
    Args:
        customer_name: Customer name to validate
        
    Returns:
        True if valid, False otherwise
    """
    return bool(re.match(CUSTOMER_NAME_PATTERN, customer_name))


def retry_on_error(max_attempts: int = MAX_RETRY_ATTEMPTS, delay: float = RETRY_DELAY):
    """
    Decorator for retrying operations on failure.
    
    Args:
        max_attempts: Maximum number of retry attempts
        delay: Delay between retries in seconds
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except (OperationalError, DatabaseError) as e:
                    if attempt == max_attempts - 1:
                        raise
                    print_warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay} seconds...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator


# =============================================================================
# SECURE CONNECTION MANAGER
# =============================================================================

class SecurePgPassManager:
    """Context manager for secure .pgpass file handling."""
    
    def __init__(self, password: str):
        self.password = password
        self.pgpass_file = None
        self.original_pgpassfile = None
        
    def __enter__(self):
        """Create secure temporary .pgpass file."""
        try:
            # Save original PGPASSFILE if it exists
            self.original_pgpassfile = os.environ.get("PGPASSFILE")
            
            # Create temporary file with secure permissions
            self.pgpass_file = tempfile.NamedTemporaryFile(
                mode='w', 
                delete=False, 
                encoding='utf-8',
                prefix='pgpass_',
                suffix='.tmp'
            )
            
            # Write connection info
            self.pgpass_file.write(f"{DB_HOST}:{DB_PORT}:*:{DB_ADMIN_USER}:{self.password}\n")
            self.pgpass_file.close()
            
            # Set secure permissions (readable only by owner)
            os.chmod(self.pgpass_file.name, PGPASS_PERMISSIONS)
            
            # Set environment variable
            os.environ["PGPASSFILE"] = self.pgpass_file.name
            
            return self.pgpass_file.name
            
        except (OSError, IOError) as e:
            raise ConfigurationError(f"Failed to create secure authentication file: {e}")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up temporary .pgpass file."""
        try:
            if self.pgpass_file and os.path.exists(self.pgpass_file.name):
                os.remove(self.pgpass_file.name)
            
            # Restore original PGPASSFILE
            if self.original_pgpassfile:
                os.environ["PGPASSFILE"] = self.original_pgpassfile
            elif "PGPASSFILE" in os.environ:
                del os.environ["PGPASSFILE"]
                
        except (OSError, IOError) as e:
            print_warning(f"Failed to clean up authentication file: {e}")


# =============================================================================
# DATABASE CONNECTION MANAGER
# =============================================================================

class DatabaseConnectionManager:
    """Context manager for database connections with proper error handling."""
    
    def __init__(self, password: str, database: str = DB_TEMPLATE):
        self.password = password
        self.database = database
        self.connection = None
        
    def __enter__(self):
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(
                user=DB_ADMIN_USER,
                host=DB_HOST,
                port=DB_PORT,
                dbname=self.database,
                password=self.password,
                connect_timeout=DB_CONNECT_TIMEOUT,
                options='-c statement_timeout={}s'.format(DB_COMMAND_TIMEOUT)
            )
            return self.connection
            
        except OperationalError as e:
            raise ConnectionError(f"Failed to connect to database: {e}")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Close database connection."""
        if self.connection:
            try:
                if exc_type is None:
                    self.connection.commit()
                else:
                    self.connection.rollback()
            except Exception as e:
                print_warning(f"Error during connection cleanup: {e}")
            finally:
                self.connection.close()


# =============================================================================
# MAIN DATABASE SETUP CLASS
# =============================================================================

class TenantDatabaseSetup:
    """
    Handles database setup for a new tenant in the LEOS360 platform.

    This class manages the complete database setup process including:
    - Database creation for Nextcloud, Keycloak, and LLDAP
    - User creation and permission assignment
    - Status checking and reset operations
    - PostgreSQL connection management with proper security
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant database setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If required files are missing
        """
        # Validate customer name
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name
        
        # Setup logging
        self.logger = setup_logging(customer_name)
        self.logger.info(f"Initializing database setup for: {customer_name}")

        # Database names (using sql.Identifier for safety)
        self.db_nextcloud = f"{customer_name}_nextcloud"
        self.db_keycloak = f"{customer_name}_keycloak"
        self.db_lldap = f"{customer_name}_lldap"
        self.db_user = f"{customer_name}_admin"

        # File paths
        self.env_file = ENV_FILE_PATTERN.format(
            base_path=BASE_TENANT_PATH,
            customer_name=customer_name
        )
        self.sql_file = SQL_FILE_PATTERN.format(
            base_path=BASE_TENANT_PATH,
            customer_name=customer_name
        )

        # Runtime variables
        self.db_admin_password = ""
        
        # Validate required files exist
        self._validate_prerequisites()

    # =========================================================================
    # VALIDATION METHODS
    # =========================================================================

    def _validate_prerequisites(self) -> None:
        """
        Validate that all required files exist.
        
        Raises:
            ConfigurationError: If required files are missing
        """
        if not Path(self.env_file).exists():
            raise ConfigurationError(f"Environment file does not exist: {self.env_file}")
        
        if not Path(self.sql_file).exists():
            raise ConfigurationError(f"SQL setup file does not exist: {self.sql_file}")

    def _validate_sql_file(self) -> None:
        """
        Validate SQL file contents for basic safety.
        
        Raises:
            ValidationError: If SQL file contains unsafe content
        """
        try:
            with open(self.sql_file, 'r', encoding='utf-8') as f:
                content = f.read().upper()
                
            # Check for dangerous operations
            dangerous_keywords = ['DROP DATABASE', 'DROP USER', 'DELETE FROM', 'TRUNCATE']
            found_dangerous = [kw for kw in dangerous_keywords if kw in content]
            
            if found_dangerous:
                self.logger.warning(f"SQL file contains potentially dangerous operations: {found_dangerous}")
            
            # Check for required operations
            required_keywords = ['CREATE DATABASE', 'CREATE USER']
            missing_required = [kw for kw in required_keywords if kw not in content]
            
            if missing_required:
                raise ValidationError(f"SQL file missing required operations: {missing_required}")
                
        except (IOError, UnicodeDecodeError) as e:
            raise ConfigurationError(f"Failed to validate SQL file: {e}")

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def read_env_file(self) -> None:
        """
        Read environment file and extract database admin password.

        Raises:
            ConfigurationError: If environment file processing fails
            ValidationError: If required environment variables are missing
        """
        try:
            dotenv.load_dotenv(self.env_file)
            self.db_admin_password = os.getenv(ENV_VAR_DB_PASSWORD)
            
            if not self.db_admin_password:
                raise ValidationError(f"{ENV_VAR_DB_PASSWORD} not found in .env file")
            
            self.logger.info("Environment file loaded successfully")
            
        except Exception as e:
            if isinstance(e, (ValidationError, ConfigurationError)):
                raise
            raise ConfigurationError(f"Failed to read environment file: {e}")

    @retry_on_error()
    def run_psql_command(self, command: List[str], error_message: str) -> subprocess.CompletedProcess:
        """
        Execute a psql command with error handling and retry logic.

        Args:
            command: List of command arguments
            error_message: Error message to display on failure

        Returns:
            CompletedProcess result

        Raises:
            DatabaseSetupError: If command execution fails after retries
        """
        try:
            self.logger.debug(f"Executing command: {' '.join(command)}")
            result = subprocess.run(
                command, 
                capture_output=True, 
                text=True, 
                timeout=DB_COMMAND_TIMEOUT,
                encoding='utf-8'
            )
            
            if result.returncode != 0:
                self.logger.error(f"Command failed with return code {result.returncode}")
                self.logger.error(f"STDERR: {result.stderr}")
                raise DatabaseSetupError(f"{error_message}: {result.stderr}")
                
            return result
            
        except subprocess.TimeoutExpired:
            raise DatabaseSetupError(f"Command timed out after {DB_COMMAND_TIMEOUT} seconds")
        except FileNotFoundError:
            raise ConfigurationError("psql command not found - PostgreSQL client not installed")

    # =========================================================================
    # DATABASE QUERY METHODS
    # =========================================================================

    def get_database_info(self, cursor) -> Dict[str, Any]:
        """
        Get comprehensive database information for this customer.

        Args:
            cursor: Database cursor

        Returns:
            Dictionary with database information
        """
        # Get existing databases
        cursor.execute(
            """
            SELECT datname FROM pg_database
            WHERE datname = ANY(%s)
            ORDER BY datname;
            """,
            ([self.db_nextcloud, self.db_keycloak, self.db_lldap],)
        )
        existing_databases = [row[0] for row in cursor.fetchall()]

        # Get user information
        cursor.execute(
            """
            SELECT rolname, rolcreatedb, rolsuper, rolcanlogin 
            FROM pg_roles
            WHERE rolname = %s;
            """,
            (self.db_user,)
        )
        user_info = cursor.fetchone()

        return {
            'databases': existing_databases,
            'expected_databases': [self.db_nextcloud, self.db_keycloak, self.db_lldap],
            'user_exists': user_info is not None,
            'user_info': dict(zip(['name', 'createdb', 'superuser', 'login'], user_info)) if user_info else None
        }

    def terminate_connections(self, cursor, database_names: List[str]) -> int:
        """
        Terminate all connections to specified databases.
        
        Args:
            cursor: Database cursor
            database_names: List of database names
            
        Returns:
            Number of terminated connections
        """
        if not database_names:
            return 0
            
        cursor.execute(
            """
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = ANY(%s) AND pid <> pg_backend_pid()
            """,
            (database_names,)
        )
        
        terminated = cursor.rowcount
        if terminated > 0:
            self.logger.info(f"Terminated {terminated} database connections")
            time.sleep(1)  # Give time for connections to close
            
        return terminated

    # =========================================================================
    # DATABASE OPERATION METHODS
    # =========================================================================

    def show_status(self) -> None:
        """
        Show comprehensive status of databases and user for this customer.
        """
        self.logger.info(f"Checking status for customer: {self.customer_name}")

        with DatabaseConnectionManager(self.db_admin_password) as conn:
            with conn.cursor() as cur:
                info = self.get_database_info(cur)

                print(f"\nStatus for customer: {self.customer_name}")
                print("=" * 50)
                
                # User status
                if info['user_exists']:
                    user_info = info['user_info']
                    print(f"✓ User: {user_info['name']}")
                    print(f"  - Can create databases: {user_info['createdb']}")
                    print(f"  - Can login: {user_info['login']}")
                    print(f"  - Is superuser: {user_info['superuser']}")
                else:
                    print(f"✗ User: {self.db_user} (not found)")

                # Database status
                print(f"\nDatabases:")
                for expected_db in info['expected_databases']:
                    if expected_db in info['databases']:
                        print(f"  ✓ {expected_db}")
                    else:
                        print(f"  ✗ {expected_db} (not found)")

                # Summary
                db_count = len(info['databases'])
                expected_count = len(info['expected_databases'])
                
                if db_count == expected_count and info['user_exists']:
                    print(f"\n✓ Setup appears complete ({db_count}/{expected_count} databases, user exists)")
                else:
                    print(f"\n⚠ Setup incomplete ({db_count}/{expected_count} databases, user {'exists' if info['user_exists'] else 'missing'})")

        print_success("Status check completed")

    def reset_databases(self, force: bool = False) -> None:
        """
        Reset (delete) all databases and user for this customer.
        
        Args:
            force: If True, skip confirmation prompt
        """
        self.logger.info(f"Resetting databases for customer: {self.customer_name}")

        with DatabaseConnectionManager(self.db_admin_password) as conn:
            with conn.cursor() as cur:
                info = self.get_database_info(cur)

                if not info['databases'] and not info['user_exists']:
                    print_warning("No database objects found to delete")
                    return

                # Show what will be deleted
                print("The following objects will be deleted:")
                if info['user_exists']:
                    print(f"  - User: {self.db_user}")
                for db_name in info['databases']:
                    print(f"  - Database: {db_name}")

                # Confirmation prompt
                if not force:
                    response = input("\nAre you sure you want to proceed? (yes/no): ").lower()
                    if response not in ['yes', 'y']:
                        print("Reset cancelled by user")
                        return

                try:
                    # Start transaction
                    conn.autocommit = False
                    
                    # Terminate connections to databases
                    if info['databases']:
                        terminated = self.terminate_connections(cur, info['databases'])
                        self.logger.info(f"Terminated {terminated} connections")

                    # Drop databases using safe SQL construction
                    for db_name in info['databases']:
                        self.logger.info(f"Dropping database: {db_name}")
                        cur.execute(sql.SQL("DROP DATABASE IF EXISTS {}").format(sql.Identifier(db_name)))

                    # Drop user
                    if info['user_exists']:
                        self.logger.info(f"Dropping user: {self.db_user}")
                        cur.execute(sql.SQL("DROP USER IF EXISTS {}").format(sql.Identifier(self.db_user)))

                    # Commit transaction
                    conn.commit()
                    conn.autocommit = True
                    
                    print_success("Database reset completed successfully")
                    self.logger.info("Reset operation completed successfully")

                except Exception as e:
                    conn.rollback()
                    conn.autocommit = True
                    self.logger.error(f"Reset operation failed: {e}")
                    raise DatabaseSetupError(f"Failed to reset databases: {e}")

    def setup_databases(self) -> None:
        """
        Set up databases and user for this customer.
        """
        self.logger.info(f"Setting up databases for customer: {self.customer_name}")

        # Validate SQL file
        self._validate_sql_file()

        with DatabaseConnectionManager(self.db_admin_password) as conn:
            with conn.cursor() as cur:
                info = self.get_database_info(cur)

                # Check if objects already exist
                if info['databases'] or info['user_exists']:
                    error_msg = f"Setup cannot be performed because objects already exist for {self.customer_name}:"
                    if info['user_exists']:
                        error_msg += f"\n- User {self.db_user} already exists"
                    for db_name in info['databases']:
                        error_msg += f"\n- Database {db_name} already exists"
                    error_msg += "\nUse --reset first to delete existing objects and then run the setup again"
                    raise DatabaseExistsError(error_msg)

        # Execute SQL setup file using psql
        with SecurePgPassManager(self.db_admin_password):
            psql_command = [
                "psql",
                "-U", DB_ADMIN_USER,
                "-h", DB_HOST,
                "-p", str(DB_PORT),
                "-d", DB_TEMPLATE,
                "-f", self.sql_file,
                "--no-psqlrc",
                "-v", "ON_ERROR_STOP=1"  # Stop on first error
            ]
            
            self.run_psql_command(psql_command, "Failed to execute database setup")

        # Verify setup was successful
        self._verify_setup()
        print_success("Database setup completed successfully")

    def _verify_setup(self) -> None:
        """
        Verify that setup was completed successfully.
        
        Raises:
            ValidationError: If verification fails
        """
        self.logger.info("Verifying database setup...")
        
        with DatabaseConnectionManager(self.db_admin_password) as conn:
            with conn.cursor() as cur:
                info = self.get_database_info(cur)
                
                # Check all databases were created
                missing_dbs = set(info['expected_databases']) - set(info['databases'])
                if missing_dbs:
                    raise ValidationError(f"Setup verification failed - missing databases: {missing_dbs}")
                
                # Check user was created
                if not info['user_exists']:
                    raise ValidationError(f"Setup verification failed - user {self.db_user} was not created")
                
                self.logger.info("Database setup verification successful")

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str, force: bool = False) -> bool:
        """
        Main execution function that orchestrates the database setup process.

        Args:
            operation: Operation to perform ('setup', 'status', or 'reset')
            force: Force operation without confirmation (for reset)

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Database Setup - {self.customer_name}")
        self.logger.info(f"Starting database {operation} for {self.customer_name}...")

        try:
            # Step 1: Read environment file
            self.read_env_file()

            # Step 2: Perform requested operation
            if operation == "status":
                self.show_status()
            elif operation == "reset":
                self.reset_databases(force=force)
            else:  # default: setup
                self.setup_databases()

            print_header("OPERATION COMPLETED SUCCESSFULLY")
            print_success(f"Database {operation} for {self.customer_name} completed successfully!")
            self.logger.info(f"Database {operation} completed successfully")

            return True

        except (ValidationError, ConfigurationError, ConnectionError, 
                DatabaseSetupError, DatabaseExistsError) as e:
            print_error(f"Database {operation} failed: {str(e)}")
            self.logger.error(f"Database {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during database {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during database {operation}: {str(e)}", exc_info=True)
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute database setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant database for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage2_tenant_db.py example-customer
  python3 stage2_tenant_db.py example-customer --status
  python3 stage2_tenant_db.py example-customer --reset
  python3 stage2_tenant_db.py example-customer --reset --force

Requirements:
  - Stage 1 must be completed successfully
  - PostgreSQL server must be accessible at ************
  - Database admin credentials must be available
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show status of databases and user'
    )
    parser.add_argument(
        '--reset',
        action='store_true',
        help='Reset (delete) existing databases and user'
    )
    parser.add_argument(
        '--force',
        action='store_true',
        help='Force operation without confirmation prompt (use with --reset)'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Validate arguments
    if args.force and not args.reset:
        print_error("--force can only be used with --reset")
        sys.exit(1)

    # Determine operation
    if args.status:
        operation = "status"
    elif args.reset:
        operation = "reset"
    else:
        operation = "setup"

    try:
        # Create and run database setup
        setup = TenantDatabaseSetup(args.customer_name)
        success = setup.run(operation, force=args.force)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Database setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()