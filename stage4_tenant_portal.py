#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 4: Portal SSO Setup
=============================================

This script handles the Portal SSO setup for a new tenant in the LEOS360 platform.
It creates organizations, identity providers, and configures SSO integration
with the central LEOS360 portal authentication system.

Author: LEOS360 Development Team
Version: 2.2
Last Updated: 2025-6-2

Prerequisites:
- Stage 1, 2, and 3 must be completed successfully
- Stage 4 Keycloak setup must be completed
- Central SSO portal must be accessible
- Environment variables must be properly configured

Usage:
    python3 stage4_tenant_portal.py <customer_name> [options]

Examples:
    python3 stage4_tenant_portal.py example-customer
    python3 stage4_tenant_portal.py example-customer --status
    python3 stage4_tenant_portal.py example-customer --validate
    python3 stage4_tenant_portal.py example-customer --export
    python3 stage4_tenant_portal.py example-customer --export --idp-alias example-customer
    python3 stage4_tenant_portal.py example-customer --backup
"""

import os
import sys
import json
import time
import argparse
import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import urllib3
from dotenv import dotenv_values

# =============================================================================
# CUSTOM EXCEPTIONS
# =============================================================================

class PortalSSOSetupError(Exception):
    """Base exception for Portal SSO setup errors."""
    pass

class ConfigurationError(PortalSSOSetupError):
    """Raised when configuration is invalid."""
    pass

class AuthenticationError(PortalSSOSetupError):
    """Raised when authentication fails."""
    pass

class OrganizationError(PortalSSOSetupError):
    """Raised when organization operations fail."""
    pass

class IdentityProviderError(PortalSSOSetupError):
    """Raised when identity provider operations fail."""
    pass

class APIError(PortalSSOSetupError):
    """Raised when API operations fail."""
    pass

class ValidationError(PortalSSOSetupError):
    """Raised when validation fails."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.2"
SCRIPT_NAME = "Stage 4: Portal SSO Setup"

# File paths
BASE_TENANT_PATH = Path("/mnt/storage/tenants")
SSO_SETUP_PATH = Path("/mnt/storage/setup/keycloak")

# Portal SSO configuration
DEFAULT_SSO_URL = "https://sso.leos360.com"
DEFAULT_REALM_NAME = "leos360"
DEFAULT_VERIFY_SSL = True

# API configuration
API_TIMEOUT = 60
API_RETRIES = 3
API_BACKOFF_FACTOR = 0.3
CONNECT_TIMEOUT = 15
READ_TIMEOUT = 45

# Token configuration
TOKEN_REFRESH_THRESHOLD = 300  # Refresh token if expires in 5 minutes
TOKEN_BUFFER_TIME = 60  # Buffer time for token expiration

# Validation patterns
CUSTOMER_NAME_PATTERN = r'^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$'
DOMAIN_PATTERN = r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$'
URL_PATTERN = r'^https?://[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*'

# Required environment variables
REQUIRED_CUSTOMER_VARS = [
    "CUSTOMER_DOMAIN",
    "KC_HOSTNAME", 
    "KEYCLOAK_REALM",
    "LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET"
]

REQUIRED_SSO_VARS = [
    "KEYCLOAK_ADMIN_USERNAME",
    "KEYCLOAK_ADMIN_PASSWORD"
]

# Log configuration
LOG_PERMISSIONS = 0o644


# =============================================================================
# DATA CLASSES
# =============================================================================

@dataclass
class PortalSSOConfig:
    """Configuration for Portal SSO setup."""
    sso_url: str
    realm_name: str
    admin_username: str
    admin_password: str
    customer_domain: str
    kc_hostname: str
    keycloak_realm: str
    client_secret: str
    verify_ssl: bool = True

@dataclass
class OrganizationInfo:
    """Information about an organization."""
    id: Optional[str]
    name: str
    enabled: bool
    domains: List[Dict[str, Any]] = field(default_factory=list)
    attributes: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IdentityProviderInfo:
    """Information about an identity provider."""
    alias: str
    display_name: str
    provider_id: str
    enabled: bool
    config: Dict[str, Any] = field(default_factory=dict)
    linked_organization: Optional[str] = None

@dataclass
class TokenInfo:
    """Information about access token."""
    access_token: str
    expires_in: int
    token_type: str = "Bearer"
    obtained_at: float = field(default_factory=time.time)
    
    @property
    def expires_at(self) -> float:
        """Calculate when token expires."""
        return self.obtained_at + self.expires_in
    
    @property
    def is_expired(self) -> bool:
        """Check if token is expired or will expire soon."""
        return time.time() >= (self.expires_at - TOKEN_BUFFER_TIME)
    
    @property
    def needs_refresh(self) -> bool:
        """Check if token needs refresh."""
        return time.time() >= (self.expires_at - TOKEN_REFRESH_THRESHOLD)


# =============================================================================
# LOGGING SETUP
# =============================================================================

def setup_logging(customer_name: str) -> logging.Logger:
    """Setup logging configuration."""
    logger = logging.getLogger('portal_sso_setup')
    logger.setLevel(logging.INFO)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if possible)
    try:
        log_dir = BASE_TENANT_PATH / customer_name / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / "portal_sso_setup.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Set log file permissions
        os.chmod(log_file, LOG_PERMISSIONS)
    except (OSError, PermissionError) as e:
        print(f"[WARNING] Could not setup file logging: {e}")
    
    return logger


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def validate_customer_name(customer_name: str) -> bool:
    """Validate customer name format."""
    return bool(re.match(CUSTOMER_NAME_PATTERN, customer_name))


def validate_domain(domain: str) -> bool:
    """Validate domain format."""
    return bool(re.match(DOMAIN_PATTERN, domain))


def validate_url(url: str) -> bool:
    """Validate URL format."""
    try:
        result = urlparse(url)
        return bool(result.scheme and result.netloc and re.match(URL_PATTERN, url))
    except Exception:
        return False


# =============================================================================
# KEYCLOAK API SESSION
# =============================================================================

class KeycloakSSOAPISession:
    """Secure HTTP session manager for Keycloak SSO API with token management."""
    
    def __init__(self, config: PortalSSOConfig):
        self.config = config
        self.session = self._create_session()
        self.token_info: Optional[TokenInfo] = None
        
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=API_RETRIES,
            backoff_factor=API_BACKOFF_FACTOR,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        session.headers.update({
            "Accept": "application/json",
            "User-Agent": f"LEOS360-Portal-SSO-Setup/{SCRIPT_VERSION}"
        })
        
        return session
    
    def authenticate(self) -> TokenInfo:
        """Authenticate and get access token."""
        url = f"{self.config.sso_url}/realms/master/protocol/openid-connect/token"
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        data = {
            "client_id": "admin-cli",
            "username": self.config.admin_username,
            "password": self.config.admin_password,
            "grant_type": "password"
        }
        
        try:
            response = self.session.post(
                url,
                headers=headers,
                data=data,
                verify=self.config.verify_ssl,
                timeout=(CONNECT_TIMEOUT, READ_TIMEOUT)
            )
            
            if response.status_code == 401:
                raise AuthenticationError("Invalid credentials")
            elif response.status_code == 403:
                raise AuthenticationError("Access forbidden - insufficient permissions")
            
            response.raise_for_status()
            
            token_data = response.json()
            
            self.token_info = TokenInfo(
                access_token=token_data.get("access_token", ""),
                expires_in=token_data.get("expires_in", 3600),
                token_type=token_data.get("token_type", "Bearer")
            )
            
            if not self.token_info.access_token:
                raise AuthenticationError("No access token received")
            
            # Update session headers with token
            self.session.headers.update({
                "Authorization": f"{self.token_info.token_type} {self.token_info.access_token}",
                "Content-Type": "application/json"
            })
            
            return self.token_info
            
        except requests.exceptions.RequestException as e:
            raise AuthenticationError(f"Failed to authenticate: {e}")
    
    def ensure_valid_token(self) -> None:
        """Ensure we have a valid access token."""
        if not self.token_info or self.token_info.needs_refresh:
            self.authenticate()
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make an authenticated API request."""
        self.ensure_valid_token()
        
        url = f"{self.config.sso_url}/{endpoint.lstrip('/')}"
        
        # Set default timeouts
        if 'timeout' not in kwargs:
            kwargs['timeout'] = (CONNECT_TIMEOUT, READ_TIMEOUT)
        
        # Set SSL verification
        if 'verify' not in kwargs:
            kwargs['verify'] = self.config.verify_ssl
        
        try:
            response = self.session.request(method=method, url=url, **kwargs)
            
            # Handle token expiration
            if response.status_code == 401:
                self.authenticate()  # Re-authenticate
                response = self.session.request(method=method, url=url, **kwargs)
            
            return response
            
        except requests.exceptions.RequestException as e:
            raise APIError(f"API request failed: {e}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()


# =============================================================================
# ORGANIZATION MANAGER
# =============================================================================

class OrganizationManager:
    """Manager for Keycloak organization operations."""
    
    def __init__(self, session: KeycloakSSOAPISession, realm_name: str, logger: logging.Logger):
        self.session = session
        self.realm_name = realm_name
        self.logger = logger
    
    def organization_exists(self, org_name: str) -> bool:
        """Check if organization exists."""
        try:
            response = self.session.request("GET", f"admin/realms/{self.realm_name}/organizations/{org_name}")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Failed to check organization existence: {e}")
            return False
    
    def get_organization(self, org_name: str) -> Optional[OrganizationInfo]:
        """Get organization information."""
        try:
            response = self.session.request("GET", f"admin/realms/{self.realm_name}/organizations/{org_name}")
            
            if response.status_code == 200:
                org_data = response.json()
                return OrganizationInfo(
                    id=org_data.get("id"),
                    name=org_data.get("name", ""),
                    enabled=org_data.get("enabled", False),
                    domains=org_data.get("domains", []),
                    attributes=org_data.get("attributes", {})
                )
            elif response.status_code == 404:
                return None
            else:
                self.logger.error(f"Failed to get organization: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to get organization: {e}")
            return None
    
    def create_organization(self, org_name: str, domain: str) -> bool:
        """Create a new organization."""
        if self.organization_exists(org_name):
            self.logger.info(f"Organization '{org_name}' already exists")
            return True
        
        org_data = {
            "name": org_name,
            "enabled": True,
            "domains": [{"name": domain, "verified": True}]
        }
        
        try:
            response = self.session.request(
                "POST",
                f"admin/realms/{self.realm_name}/organizations",
                json=org_data
            )
            
            if response.status_code in [201, 409]:  # 409 = conflict (already exists)
                self.logger.info(f"Organization '{org_name}' created successfully")
                time.sleep(1)  # Brief pause for resource availability
                return True
            else:
                self.logger.error(f"Failed to create organization: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to create organization: {e}")
            return False
    
    def list_organizations(self) -> List[OrganizationInfo]:
        """List all organizations."""
        try:
            response = self.session.request("GET", f"admin/realms/{self.realm_name}/organizations")
            
            if response.status_code == 200:
                orgs_data = response.json()
                organizations = []
                
                for org_data in orgs_data:
                    org = OrganizationInfo(
                        id=org_data.get("id"),
                        name=org_data.get("name", ""),
                        enabled=org_data.get("enabled", False),
                        domains=org_data.get("domains", []),
                        attributes=org_data.get("attributes", {})
                    )
                    organizations.append(org)
                
                return organizations
            else:
                self.logger.error(f"Failed to list organizations: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"Failed to list organizations: {e}")
            return []
    
    def get_organization_id(self, org_name: str) -> Optional[str]:
        """Get organization ID by name."""
        org = self.get_organization(org_name)
        return org.id if org else None


# =============================================================================
# IDENTITY PROVIDER MANAGER
# =============================================================================

class IdentityProviderManager:
    """Manager for Keycloak identity provider operations."""
    
    def __init__(self, session: KeycloakSSOAPISession, realm_name: str, logger: logging.Logger):
        self.session = session
        self.realm_name = realm_name
        self.logger = logger
    
    def identity_provider_exists(self, idp_alias: str) -> bool:
        """Check if identity provider exists."""
        try:
            response = self.session.request(
                "GET", 
                f"admin/realms/{self.realm_name}/identity-provider/instances/{idp_alias}"
            )
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Failed to check identity provider existence: {e}")
            return False
    
    def get_identity_provider(self, idp_alias: str) -> Optional[IdentityProviderInfo]:
        """Get identity provider information."""
        try:
            response = self.session.request(
                "GET",
                f"admin/realms/{self.realm_name}/identity-provider/instances/{idp_alias}"
            )
            
            if response.status_code == 200:
                idp_data = response.json()
                return IdentityProviderInfo(
                    alias=idp_data.get("alias", ""),
                    display_name=idp_data.get("displayName", ""),
                    provider_id=idp_data.get("providerId", ""),
                    enabled=idp_data.get("enabled", False),
                    config=idp_data.get("config", {})
                )
            elif response.status_code == 404:
                return None
            else:
                self.logger.error(f"Failed to get identity provider: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to get identity provider: {e}")
            return None
    
    def create_identity_provider(self, config: PortalSSOConfig, idp_alias: str) -> bool:
        """Create OIDC identity provider."""
        if self.identity_provider_exists(idp_alias):
            self.logger.info(f"Identity provider '{idp_alias}' already exists")
            return True
        
        idp_config = self._build_idp_config(config, idp_alias)
        
        try:
            response = self.session.request(
                "POST",
                f"admin/realms/{self.realm_name}/identity-provider/instances",
                json=idp_config
            )
            
            if response.status_code in [201, 409]:
                self.logger.info(f"Identity provider '{idp_alias}' created successfully")
                return True
            else:
                self.logger.error(f"Failed to create identity provider: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to create identity provider: {e}")
            return False
    
    def _build_idp_config(self, config: PortalSSOConfig, idp_alias: str) -> Dict[str, Any]:
        """Build identity provider configuration."""
        base_url = config.kc_hostname.rstrip('/')
        realm = config.keycloak_realm
        
        return {
            "alias": idp_alias,
            "displayName": config.customer_domain,
            "providerId": "keycloak-oidc",
            "enabled": True,
            "updateProfileFirstLoginMode": "on",
            "trustEmail": False,
            "storeToken": False,
            "addReadTokenRoleOnCreate": False,
            "authenticateByDefault": False,
            "linkOnly": False,
            "hideOnLogin": True,
            "config": {
                "tokenUrl": f"{base_url}/realms/{realm}/protocol/openid-connect/token",
                "acceptsPromptNoneForwardFromClient": "false",
                "jwksUrl": f"{base_url}/realms/{realm}/protocol/openid-connect/certs",
                "isAccessTokenJWT": "true",
                "filteredByClaim": "false",
                "backchannelSupported": "false",
                "caseSensitiveOriginalUsername": "false",
                "issuer": f"{base_url}/realms/{realm}",
                "pkceMethod": "S256",
                "loginHint": "true",
                "clientAuthMethod": "client_secret_post",
                "syncMode": "LEGACY",
                "clientSecret": config.client_secret,
                "allowedClockSkew": "0",
                "userInfoUrl": f"{base_url}/realms/{realm}/protocol/openid-connect/userinfo",
                "validateSignature": "true",
                "clientId": "leos360portal",
                "uiLocales": "false",
                "disableNonce": "false",
                "useJwksUrl": "true",
                "kc.org.domain": config.customer_domain,
                "kc.org.broker.redirect.mode.email-matches": "true",
                "sendClientIdOnLogout": "false",
                "pkceEnabled": "true",
                "metadataDescriptorUrl": f"{base_url}/realms/{realm}/.well-known/openid-configuration",
                "authorizationUrl": f"{base_url}/realms/{realm}/protocol/openid-connect/auth",
                "disableUserInfo": "false",
                "logoutUrl": f"{base_url}/realms/{realm}/protocol/openid-connect/logout",
                "sendIdTokenOnLogout": "true",
                "passMaxAge": "false"
            }
        }
    
    def list_identity_providers(self) -> List[IdentityProviderInfo]:
        """List all identity providers."""
        try:
            response = self.session.request(
                "GET",
                f"admin/realms/{self.realm_name}/identity-provider/instances"
            )
            
            if response.status_code == 200:
                idps_data = response.json()
                identity_providers = []
                
                for idp_data in idps_data:
                    idp = IdentityProviderInfo(
                        alias=idp_data.get("alias", ""),
                        display_name=idp_data.get("displayName", ""),
                        provider_id=idp_data.get("providerId", ""),
                        enabled=idp_data.get("enabled", False),
                        config=idp_data.get("config", {})
                    )
                    identity_providers.append(idp)
                
                return identity_providers
            else:
                self.logger.error(f"Failed to list identity providers: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"Failed to list identity providers: {e}")
            return []
    
    def link_to_organization(self, org_id: str, idp_alias: str) -> bool:
        """Link identity provider to organization."""
        try:
            response = self.session.request(
                "POST",
                f"admin/realms/{self.realm_name}/organizations/{org_id}/identity-providers",
                json=idp_alias
            )
            
            if response.status_code in [200, 204, 409]:
                self.logger.info(f"Identity provider '{idp_alias}' linked to organization successfully")
                return True
            else:
                self.logger.error(f"Failed to link identity provider: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to link identity provider to organization: {e}")
            return False


# =============================================================================
# MAIN PORTAL SSO SETUP CLASS
# =============================================================================

class TenantPortalSSOSetup:
    """
    Handles Portal SSO setup for a new tenant in the LEOS360 platform.

    This class manages the complete Portal SSO setup process including:
    - Configuration loading and validation
    - Keycloak admin authentication and token management
    - Organization creation and configuration
    - Identity provider setup and linking
    - SSO integration with central LEOS360 portal
    - Status checking, validation, and export operations
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Portal SSO setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If required files are missing
        """
        # Validate and normalize customer name
        customer_name = customer_name.strip().lower()
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name
        
        # Setup logging
        self.logger = setup_logging(customer_name)
        self.logger.info(f"Initializing Portal SSO setup for: {customer_name}")

        # File paths
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"
        self.sso_env_file = SSO_SETUP_PATH / ".env"

        # Validate required files exist
        self._validate_prerequisites()

        # Configuration (will be loaded later)
        self.config: Optional[PortalSSOConfig] = None
        
        # Managers (will be initialized after configuration)
        self.session: Optional[KeycloakSSOAPISession] = None
        self.org_manager: Optional[OrganizationManager] = None
        self.idp_manager: Optional[IdentityProviderManager] = None

    # =========================================================================
    # VALIDATION AND CONFIGURATION METHODS
    # =========================================================================

    def _validate_prerequisites(self) -> None:
        """Validate that all required files exist."""
        missing_files = []
        
        if not self.customer_dir.exists():
            missing_files.append(f"Customer directory: {self.customer_dir}")
        
        if not self.env_file.exists():
            missing_files.append(f"Customer .env file: {self.env_file}")
            
        if not self.sso_env_file.exists():
            missing_files.append(f"SSO .env file: {self.sso_env_file}")
        
        if missing_files:
            raise ConfigurationError(f"Required files missing: {', '.join(missing_files)}")

    def load_configuration(self) -> None:
        """Load configuration from environment files with validation."""
        self.logger.info("Loading configuration from environment files")
        
        try:
            # Load customer environment variables
            customer_env = dotenv_values(self.env_file)
            
            # Load SSO environment variables
            sso_env = dotenv_values(self.sso_env_file)
            
            # Validate required customer variables
            missing_customer_vars = [var for var in REQUIRED_CUSTOMER_VARS if var not in customer_env or not customer_env[var]]
            if missing_customer_vars:
                raise ConfigurationError(f"Missing required customer variables: {missing_customer_vars}")
            
            # Validate required SSO variables
            missing_sso_vars = [var for var in REQUIRED_SSO_VARS if var not in sso_env or not sso_env[var]]
            if missing_sso_vars:
                raise ConfigurationError(f"Missing required SSO variables: {missing_sso_vars}")
            
            # Validate formats
            customer_domain = customer_env["CUSTOMER_DOMAIN"]
            if not validate_domain(customer_domain):
                raise ConfigurationError(f"Invalid CUSTOMER_DOMAIN format: {customer_domain}")
            
            kc_hostname = customer_env["KC_HOSTNAME"].rstrip('/')
            if not validate_url(kc_hostname):
                raise ConfigurationError(f"Invalid KC_HOSTNAME format: {kc_hostname}")
            
            # Get SSO URL from environment or use default
            sso_url = sso_env.get("SSO_URL", DEFAULT_SSO_URL).rstrip('/')
            if not validate_url(sso_url):
                raise ConfigurationError(f"Invalid SSO_URL format: {sso_url}")
            
            # Validate client secret
            client_secret = customer_env["LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET"]
            if len(client_secret) < 20:
                raise ConfigurationError("LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET appears too short")
            
            # Build configuration
            self.config = PortalSSOConfig(
                sso_url=sso_url,
                realm_name=sso_env.get("REALM_NAME", DEFAULT_REALM_NAME),
                admin_username=sso_env["KEYCLOAK_ADMIN_USERNAME"],
                admin_password=sso_env["KEYCLOAK_ADMIN_PASSWORD"],
                customer_domain=customer_domain,
                kc_hostname=kc_hostname,
                keycloak_realm=customer_env["KEYCLOAK_REALM"],
                client_secret=client_secret,
                verify_ssl=sso_env.get("VERIFY_SSL", "true").lower() == "true"
            )

            self.logger.info("Configuration loaded successfully")

        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"Failed to load configuration: {e}")

    def _initialize_managers(self) -> None:
        """Initialize API session and managers."""
        if not self.config:
            raise ConfigurationError("Configuration not loaded")

        # Disable SSL warnings if SSL verification is disabled
        if not self.config.verify_ssl:
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            self.logger.warning("SSL verification is disabled")

        # Initialize API session
        self.session = KeycloakSSOAPISession(self.config)
        
        # Test authentication
        try:
            self.session.authenticate()
            self.logger.info("Keycloak SSO authentication successful")
        except AuthenticationError as e:
            raise AuthenticationError(f"Failed to authenticate with SSO portal: {e}")

        # Initialize managers
        self.org_manager = OrganizationManager(self.session, self.config.realm_name, self.logger)
        self.idp_manager = IdentityProviderManager(self.session, self.config.realm_name, self.logger)

    # =========================================================================
    # MAIN OPERATION METHODS
    # =========================================================================

    def setup_organization(self) -> bool:
        """Setup organization in SSO portal."""
        print_step("Setting up organization in SSO portal...")
        
        try:
            success = self.org_manager.create_organization(self.customer_name, self.config.customer_domain)
            if success:
                print_success(f"Organization '{self.customer_name}' is ready")
            else:
                print_error(f"Failed to create organization '{self.customer_name}'")
            
            return success
            
        except Exception as e:
            print_error(f"Failed to setup organization: {e}")
            self.logger.error(f"Failed to setup organization: {e}", exc_info=True)
            return False

    def setup_identity_provider(self) -> bool:
        """Setup identity provider in SSO portal."""
        print_step("Setting up identity provider in SSO portal...")
        
        try:
            success = self.idp_manager.create_identity_provider(self.config, self.customer_name)
            if success:
                print_success(f"Identity provider '{self.customer_name}' is ready")
            else:
                print_error(f"Failed to create identity provider '{self.customer_name}'")
            
            return success
            
        except Exception as e:
            print_error(f"Failed to setup identity provider: {e}")
            self.logger.error(f"Failed to setup identity provider: {e}", exc_info=True)
            return False

    def link_idp_to_organization(self) -> bool:
        """Link identity provider to organization."""
        print_step("Linking identity provider to organization...")
        
        try:
            # Get organization ID
            org_id = self.org_manager.get_organization_id(self.customer_name)
            if not org_id:
                print_error("Failed to get organization ID for linking")
                return False

            success = self.idp_manager.link_to_organization(org_id, self.customer_name)
            if success:
                print_success("Identity provider linked to organization successfully")
            else:
                print_error("Failed to link identity provider to organization")
            
            return success
            
        except Exception as e:
            print_error(f"Failed to link identity provider: {e}")
            self.logger.error(f"Failed to link identity provider: {e}", exc_info=True)
            return False

    # =========================================================================
    # STATUS AND VALIDATION METHODS
    # =========================================================================

    def show_status(self) -> bool:
        """Show current status of Portal SSO setup."""
        print_step(f"Checking Portal SSO status for customer: {self.customer_name}")

        try:
            if not self.config:
                self.load_configuration()
                self._initialize_managers()

            print(f"\nPortal SSO Status for customer: {self.customer_name}")
            print("=" * 60)

            # Check organization
            org = self.org_manager.get_organization(self.customer_name)
            if org:
                print(f"✓ Organization: {org.name}")
                print(f"  - ID: {org.id}")
                print(f"  - Enabled: {org.enabled}")
                if org.domains:
                    print(f"  - Domains:")
                    for domain in org.domains:
                        verified = "✓" if domain.get("verified", False) else "✗"
                        print(f"    - {domain.get('name', 'Unknown')} {verified}")
            else:
                print(f"✗ Organization: {self.customer_name} (not found)")

            # Check identity provider
            idp = self.idp_manager.get_identity_provider(self.customer_name)
            if idp:
                print(f"✓ Identity Provider: {idp.alias}")
                print(f"  - Display Name: {idp.display_name}")
                print(f"  - Provider ID: {idp.provider_id}")
                print(f"  - Enabled: {idp.enabled}")
                
                # Check key configuration
                client_id = idp.config.get("clientId", "N/A")
                issuer = idp.config.get("issuer", "N/A")
                print(f"  - Client ID: {client_id}")
                print(f"  - Issuer: {issuer}")
            else:
                print(f"✗ Identity Provider: {self.customer_name} (not found)")

            # Summary
            org_ok = org is not None
            idp_ok = idp is not None
            
            if org_ok and idp_ok:
                print(f"\n✓ Portal SSO for {self.customer_name} is properly configured")
            else:
                print(f"\n⚠ Portal SSO for {self.customer_name} is incomplete")

            return True

        except Exception as e:
            print_error(f"Failed to check status: {e}")
            self.logger.error(f"Status check failed: {e}", exc_info=True)
            return False

    def validate_configuration(self) -> bool:
        """Validate the Portal SSO configuration."""
        print_step(f"Validating Portal SSO configuration for customer: {self.customer_name}")

        validation_errors = []

        try:
            # Load configuration if not already loaded
            if not self.config:
                self.load_configuration()
                self._initialize_managers()

            # Test SSO portal connectivity
            try:
                self.session.ensure_valid_token()
                print_success("SSO portal connectivity test passed")
            except Exception as e:
                validation_errors.append(f"SSO portal connectivity failed: {e}")

            # Validate organization configuration
            org = self.org_manager.get_organization(self.customer_name)
            if not org:
                validation_errors.append("Organization not found")
            elif not org.enabled:
                validation_errors.append("Organization is disabled")
            elif not org.domains:
                validation_errors.append("Organization has no domains configured")
            else:
                # Check domain verification
                domain_verified = any(d.get("verified", False) for d in org.domains)
                if not domain_verified:
                    validation_errors.append("No verified domains found")

            # Validate identity provider configuration
            idp = self.idp_manager.get_identity_provider(self.customer_name)
            if not idp:
                validation_errors.append("Identity provider not found")
            elif not idp.enabled:
                validation_errors.append("Identity provider is disabled")
            else:
                # Check key configuration values
                required_config = ["clientId", "clientSecret", "issuer", "authorizationUrl", "tokenUrl"]
                missing_config = [key for key in required_config if not idp.config.get(key)]
                if missing_config:
                    validation_errors.append(f"Identity provider missing configuration: {missing_config}")

            # Validate connectivity to customer Keycloak
            try:
                well_known_url = f"{self.config.kc_hostname}/realms/{self.config.keycloak_realm}/.well-known/openid-configuration"
                response = requests.get(well_known_url, timeout=10, verify=self.config.verify_ssl)
                if response.status_code != 200:
                    validation_errors.append(f"Customer Keycloak unreachable: {well_known_url}")
            except Exception as e:
                validation_errors.append(f"Customer Keycloak connectivity failed: {e}")

            if validation_errors:
                print_error("Configuration validation failed:")
                for error in validation_errors:
                    print_error(f"  - {error}")
                return False
            else:
                print_success(f"Portal SSO configuration for {self.customer_name} is valid")
                return True

        except Exception as e:
            print_error(f"Configuration validation failed: {e}")
            self.logger.error(f"Validation failed: {e}", exc_info=True)
            return False

    def export_configuration(self, idp_alias: Optional[str] = None) -> bool:
        """Export identity provider configuration."""
        if idp_alias:
            print_step(f"Exporting identity provider '{idp_alias}' configuration...")
        else:
            print_step("Listing all identity providers...")

        try:
            if not self.config:
                self.load_configuration()
                self._initialize_managers()

            if idp_alias:
                # Export specific identity provider
                idp = self.idp_manager.get_identity_provider(idp_alias)
                if idp:
                    print_header(f"Identity Provider '{idp_alias}' Configuration")
                    
                    # Create export data
                    export_data = {
                        "alias": idp.alias,
                        "displayName": idp.display_name,
                        "providerId": idp.provider_id,
                        "enabled": idp.enabled,
                        "config": idp.config
                    }
                    
                    print(json.dumps(export_data, indent=2))
                    print_success(f"Identity provider '{idp_alias}' configuration exported")
                    return True
                else:
                    print_error(f"Identity provider '{idp_alias}' not found")
                    return False
            else:
                # List all identity providers
                idp_list = self.idp_manager.list_identity_providers()
                
                print_header(f"Identity Providers in realm '{self.config.realm_name}'")
                for idp in idp_list:
                    enabled_icon = "✓" if idp.enabled else "✗"
                    print(f"{enabled_icon} {idp.alias} ({idp.provider_id}) - {idp.display_name}")
                
                print_success(f"Found {len(idp_list)} identity providers")
                return True

        except Exception as e:
            print_error(f"Export failed: {e}")
            self.logger.error(f"Export failed: {e}", exc_info=True)
            return False

    def backup_configuration(self) -> bool:
        """Backup current Portal SSO configuration."""
        print_step(f"Creating backup of Portal SSO configuration for {self.customer_name}")

        try:
            if not self.config:
                self.load_configuration()
                self._initialize_managers()

            # Create backup directory
            backup_dir = self.customer_dir / "backups" / "portal_sso"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"portal_sso_backup_{timestamp}.json"

            # Get current configuration
            org = self.org_manager.get_organization(self.customer_name)
            idp = self.idp_manager.get_identity_provider(self.customer_name)

            backup_data = {
                "customer_name": self.customer_name,
                "timestamp": timestamp,
                "sso_url": self.config.sso_url,
                "realm_name": self.config.realm_name,
                "organization": {
                    "id": org.id if org else None,
                    "name": org.name if org else None,
                    "enabled": org.enabled if org else None,
                    "domains": org.domains if org else None,
                    "attributes": org.attributes if org else None
                } if org else None,
                "identity_provider": {
                    "alias": idp.alias if idp else None,
                    "display_name": idp.display_name if idp else None,
                    "provider_id": idp.provider_id if idp else None,
                    "enabled": idp.enabled if idp else None,
                    "config": idp.config if idp else None
                } if idp else None
            }

            # Save backup
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2)
            
            os.chmod(backup_file, LOG_PERMISSIONS)
            
            print_success(f"Configuration backed up to: {backup_file}")
            return True

        except Exception as e:
            print_error(f"Backup failed: {e}")
            self.logger.error(f"Backup failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str, **kwargs) -> bool:
        """
        Main execution function that orchestrates the Portal SSO operations.

        Args:
            operation: Operation to perform
            **kwargs: Additional operation-specific arguments

        Returns:
            True if operation completed successfully
        """
        print_header(f"LEOS360 Portal SSO Setup - {self.customer_name}")
        self.logger.info(f"Starting Portal SSO {operation} for {self.customer_name}")

        try:
            # Load configuration and initialize managers
            self.load_configuration()
            self._initialize_managers()

            # Handle different operations
            if operation == "status":
                return self.show_status()
            elif operation == "validate":
                return self.validate_configuration()
            elif operation == "export":
                idp_alias = kwargs.get("idp_alias")
                return self.export_configuration(idp_alias)
            elif operation == "backup":
                return self.backup_configuration()
            else:  # default: setup
                success = True
                
                # Execute setup steps
                success &= self.setup_organization()
                success &= self.setup_identity_provider()
                success &= self.link_idp_to_organization()

                if success:
                    print_header("OPERATION COMPLETED SUCCESSFULLY")
                    print_success(f"Portal SSO setup for {self.customer_name} completed successfully!")
                    self.logger.info("Portal SSO setup completed successfully")
                else:
                    print_error("Portal SSO setup completed with errors")
                    self.logger.error("Portal SSO setup completed with errors")

                return success

        except (ConfigurationError, ValidationError, AuthenticationError, APIError) as e:
            print_error(f"Portal SSO {operation} failed: {str(e)}")
            self.logger.error(f"Portal SSO {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during Portal SSO {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during Portal SSO {operation}: {str(e)}", exc_info=True)
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute Portal SSO setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup Portal SSO for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage4_tenant_portal.py example-customer
  python3 stage4_tenant_portal.py example-customer --status
  python3 stage4_tenant_portal.py example-customer --validate
  python3 stage4_tenant_portal.py example-customer --export
  python3 stage4_tenant_portal.py example-customer --export --idp-alias example-customer
  python3 stage4_tenant_portal.py example-customer --backup

Requirements:
  - Stage 1, 2, and 3 must be completed successfully
  - Stage 4 Keycloak setup must be completed
  - Central SSO portal must be accessible
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match previous stages)'
    )
    
    operation_group = parser.add_mutually_exclusive_group()
    operation_group.add_argument(
        '--status',
        action='store_true',
        help='Show status of the Portal SSO setup'
    )
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate the Portal SSO configuration'
    )
    operation_group.add_argument(
        '--export',
        action='store_true',
        help='Export identity provider configuration'
    )
    operation_group.add_argument(
        '--backup',
        action='store_true',
        help='Backup current Portal SSO configuration'
    )
    
    parser.add_argument(
        '--idp-alias',
        help='Identity provider alias for export (optional, lists all if not specified)'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation and arguments
    if args.status:
        operation = "status"
        kwargs = {}
    elif args.validate:
        operation = "validate"
        kwargs = {}
    elif args.export:
        operation = "export"
        kwargs = {"idp_alias": args.idp_alias}
    elif args.backup:
        operation = "backup"
        kwargs = {}
    else:
        operation = "setup"
        kwargs = {}

    try:
        # Create and run Portal SSO setup
        setup = TenantPortalSSOSetup(args.customer_name)
        success = setup.run(operation, **kwargs)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Portal SSO setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()