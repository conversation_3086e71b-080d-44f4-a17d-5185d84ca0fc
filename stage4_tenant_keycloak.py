#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 4: Keycloak Setup
===========================================

This script handles the Keycloak setup for a new tenant in the LEOS360 platform.
It creates realms, clients, LDAP federation, and configures authentication flows
for the complete identity management infrastructure.

Author: LEOS360 Development Team
Version: 2.2
Last Updated: 2025-6-2

Prerequisites:
- Stage 1, 2, and 3 must be completed successfully
- Keycloak server must be accessible
- LLDAP container must be running and configured
- Environment variables must be properly configured

Usage:
    python3 stage4_tenant_keycloak.py <customer_name> [options]

Examples:
    python3 stage4_tenant_keycloak.py example-customer
    python3 stage4_tenant_keycloak.py example-customer --status
    python3 stage4_tenant_keycloak.py example-customer --validate
    python3 stage4_tenant_keycloak.py example-customer --sync-only
"""

import os
import sys
import time
import argparse
import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from urllib.parse import urlparse
import requests
import urllib3

# Import common functions from LEOS360 platform
from leos360_common import (
    # Exception classes
    LEOS360Error, ConfigurationError, ValidationError, APIError,
    # Logging functions
    setup_logging,
    # Print functions
    print_header, print_step, print_warning, print_error, print_success,
    # Validation functions
    validate_customer_name,
    # Environment handling
    load_env_file, get_env_variable,
    # HTTP session management
    APISession,
    # Constants
    TENANTS_BASE_PATH
)

# =============================================================================
# KEYCLOAK-SPECIFIC EXCEPTIONS
# =============================================================================

class KeycloakSetupError(LEOS360Error):
    """Base exception for Keycloak setup errors."""
    pass

class AuthenticationError(KeycloakSetupError):
    """Raised when authentication fails."""
    pass

class RealmError(KeycloakSetupError):
    """Raised when realm operations fail."""
    pass

class ClientError(KeycloakSetupError):
    """Raised when client operations fail."""
    pass

class LDAPError(KeycloakSetupError):
    """Raised when LDAP operations fail."""
    pass

class RoleError(KeycloakSetupError):
    """Raised when role operations fail."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "3.0"
SCRIPT_NAME = "Stage 4: Keycloak Setup"

# File paths
BASE_TENANT_PATH = TENANTS_BASE_PATH  # Use common constant

# Keycloak configuration
DEFAULT_VERIFY_SSL = True
API_TIMEOUT = 60
API_RETRIES = 3
API_BACKOFF_FACTOR = 0.3
CONNECT_TIMEOUT = 15
READ_TIMEOUT = 45

# LDAP configuration
LDAP_CONNECTION_TIMEOUT = 30
LDAP_SYNC_TIMEOUT = 120
LDAP_PORT = 3890

# Keycloak-specific validation patterns
REALM_NAME_PATTERN = r'^[a-zA-Z0-9]([a-zA-Z0-9._-]{0,214}[a-zA-Z0-9])?$'
CLIENT_ID_PATTERN = r'^[a-zA-Z0-9]([a-zA-Z0-9._-]{0,63}[a-zA-Z0-9])?$'

# Required environment variables
REQUIRED_ENV_VARS = [
    "KC_HOSTNAME",
    "KEYCLOAK_ADMIN_USERNAME", 
    "KEYCLOAK_ADMIN_PASSWORD",
    "KEYCLOAK_REALM",
    "CUSTOMER_DOMAIN",
    "LLDAP_BASE_DN",
    "LLDAP_RO_PASS",
    "CUSTOMER_IP",
    "NEXTCLOUD_KEYCLOAK_CLIENT_SECRET",
    "LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET"
]


# =============================================================================
# DATA CLASSES
# =============================================================================

@dataclass
class KeycloakConfig:
    """Configuration for Keycloak setup."""
    hostname: str
    admin_username: str
    admin_password: str
    realm: str
    customer_domain: str
    base_dn: str
    ldap_ro_pass: str
    customer_ip: str
    customer_name: str
    base_domain: str
    nextcloud_client_secret: str
    leos360portal_client_secret: str
    verify_ssl: bool = True

@dataclass
class ClientConfig:
    """Configuration for a Keycloak client."""
    client_id: str
    name: str
    description: str
    root_url: str
    redirect_uris: List[str]
    web_origins: List[str]
    secret: str
    protocol_mappers: List[Dict[str, Any]] = field(default_factory=list)
    default_client_scopes: List[str] = field(default_factory=list)
    optional_client_scopes: List[str] = field(default_factory=list)

@dataclass
class LDAPConfig:
    """Configuration for LDAP federation."""
    connection_url: str
    bind_dn: str
    bind_credential: str
    users_dn: str
    base_dn: str
    username_attribute: str = "uid"
    uuid_attribute: str = "uid"
    rdn_attribute: str = "uid"
    user_object_classes: str = "person"

@dataclass
class SetupStatus:
    """Status tracking for setup operations."""
    realm_created: bool = False
    ldap_federation_created: bool = False
    ldap_mappers_configured: bool = False
    ldap_synced: bool = False
    nextcloud_client_created: bool = False
    portal_client_created: bool = False
    nextcloud_roles_created: bool = False
    portal_roles_created: bool = False
    role_mappings_created: bool = False


# =============================================================================
# KEYCLOAK-SPECIFIC UTILITY FUNCTIONS
# =============================================================================


def validate_realm_name(realm_name: str) -> bool:
    """Validate realm name format."""
    return bool(re.match(REALM_NAME_PATTERN, realm_name))


def validate_client_id(client_id: str) -> bool:
    """Validate client ID format."""
    return bool(re.match(CLIENT_ID_PATTERN, client_id))


def validate_hostname(hostname: str) -> bool:
    """Validate hostname format."""
    try:
        # Check if it's a valid URL or hostname
        if hostname.startswith(('http://', 'https://')):
            parsed = urlparse(hostname)
            return bool(parsed.netloc)
        else:
            # Simple hostname validation
            return re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$', hostname) is not None
    except Exception:
        return False


# =============================================================================
# KEYCLOAK API SESSION
# =============================================================================

class KeycloakAPISession(APISession):
    """Secure HTTP session manager for Keycloak API with retry logic."""

    def __init__(self, config: KeycloakConfig):
        self.config = config
        self.access_token: Optional[str] = None

        # Build base URL for Keycloak API
        hostname = config.hostname.rstrip('/')
        if not hostname.startswith(('http://', 'https://')):
            hostname = f"https://{hostname}"

        # Custom headers for Keycloak API
        headers = {
            "Accept": "application/json",
            "User-Agent": f"LEOS360-Keycloak-Setup/{SCRIPT_VERSION}"
        }

        # Initialize parent APISession
        super().__init__(
            base_url=hostname,
            timeout=API_TIMEOUT,
            retries=API_RETRIES,
            backoff_factor=API_BACKOFF_FACTOR,
            headers=headers
        )
    
    def authenticate(self) -> None:
        """Authenticate with Keycloak and get access token."""
        url = f"{self.base_url}/realms/master/protocol/openid-connect/token"
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        data = {
            "client_id": "admin-cli",
            "username": self.config.admin_username,
            "password": self.config.admin_password,
            "grant_type": "password"
        }
        
        try:
            response = self.session.post(
                url, 
                headers=headers, 
                data=data, 
                verify=self.config.verify_ssl,
                timeout=(CONNECT_TIMEOUT, READ_TIMEOUT)
            )
            
            if response.status_code == 401:
                raise AuthenticationError("Invalid credentials")
            elif response.status_code == 403:
                raise AuthenticationError("Access forbidden - insufficient permissions")
            
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data.get("access_token")
            
            if not self.access_token:
                raise AuthenticationError("No access token received")
            
            # Update session headers with token
            self.session.headers.update({
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            })
            
        except requests.exceptions.RequestException as e:
            raise AuthenticationError(f"Failed to authenticate: {e}")
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make an authenticated API request with Keycloak-specific handling."""
        if not self.access_token:
            self.authenticate()

        # Set SSL verification for Keycloak
        if 'verify' not in kwargs:
            kwargs['verify'] = self.config.verify_ssl

        try:
            # Use parent's request method
            response = super().request(method, endpoint, **kwargs)
            return response

        except APIError as e:
            # Handle token expiration specifically
            if "401" in str(e):
                self.authenticate()  # Re-authenticate
                # Update Authorization header and retry
                self.session.headers.update({
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json"
                })
                response = super().request(method, endpoint, **kwargs)
                return response
            else:
                raise KeycloakSetupError(f"API request failed: {e}")


# =============================================================================
# KEYCLOAK REALM MANAGER
# =============================================================================

class KeycloakRealmManager:
    """Manager for Keycloak realm operations."""
    
    def __init__(self, session: KeycloakAPISession, logger: logging.Logger):
        self.session = session
        self.logger = logger
    
    def realm_exists(self, realm_name: str) -> bool:
        """Check if a realm exists."""
        try:
            response = self.session.request("GET", f"admin/realms/{realm_name}")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Failed to check realm existence: {e}")
            return False
    
    def create_realm(self, realm_name: str, realm_config: Optional[Dict[str, Any]] = None) -> bool:
        """Create a new realm."""
        if self.realm_exists(realm_name):
            self.logger.info(f"Realm '{realm_name}' already exists")
            return True
        
        # Default realm configuration
        default_config = {
            "realm": realm_name,
            "enabled": True,
            "displayName": realm_name,
            "accessTokenLifespan": 3600,
            "ssoSessionIdleTimeout": 1800,
            "ssoSessionMaxLifespan": 36000,
            "loginWithEmailAllowed": True,
            "registrationAllowed": False,
            "rememberMe": True,
            "verifyEmail": False,
            "resetPasswordAllowed": True,
            "editUsernameAllowed": False,
            "bruteForceProtected": True,
            "permanentLockout": False,
            "maxFailureWaitSeconds": 900,
            "minimumQuickLoginWaitSeconds": 60,
            "waitIncrementSeconds": 60,
            "quickLoginCheckMilliSeconds": 1000,
            "maxDeltaTimeSeconds": 43200,
            "failureFactor": 30
        }
        
        # Merge with provided config
        if realm_config:
            default_config.update(realm_config)
        
        try:
            response = self.session.request("POST", "admin/realms", json=default_config)
            
            if response.status_code in [201, 409]:  # 409 = conflict (already exists)
                self.logger.info(f"Realm '{realm_name}' created successfully")
                return True
            else:
                self.logger.error(f"Failed to create realm: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to create realm: {e}")
            return False
    
    def get_realm_info(self, realm_name: str) -> Optional[Dict[str, Any]]:
        """Get realm information."""
        try:
            response = self.session.request("GET", f"admin/realms/{realm_name}")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.logger.error(f"Failed to get realm info: {e}")
            return None


# =============================================================================
# KEYCLOAK CLIENT MANAGER
# =============================================================================

class KeycloakClientManager:
    """Manager for Keycloak client operations."""
    
    def __init__(self, session: KeycloakAPISession, realm_name: str, logger: logging.Logger):
        self.session = session
        self.realm_name = realm_name
        self.logger = logger
    
    def client_exists(self, client_id: str) -> bool:
        """Check if a client exists."""
        clients = self.get_clients()
        return any(client.get('clientId') == client_id for client in clients)
    
    def get_clients(self) -> List[Dict[str, Any]]:
        """Get all clients in the realm."""
        try:
            response = self.session.request("GET", f"admin/realms/{self.realm_name}/clients")
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            self.logger.error(f"Failed to get clients: {e}")
            return []
    
    def get_client_by_id(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get client by client ID."""
        clients = self.get_clients()
        for client in clients:
            if client.get('clientId') == client_id:
                return client
        return None
    
    def create_client(self, client_config: ClientConfig) -> Optional[str]:
        """Create a new client."""
        if self.client_exists(client_config.client_id):
            self.logger.info(f"Client '{client_config.client_id}' already exists")
            client = self.get_client_by_id(client_config.client_id)
            return client.get('id') if client else None
        
        # Build client data
        client_data = self._build_client_data(client_config)
        
        try:
            response = self.session.request(
                "POST", 
                f"admin/realms/{self.realm_name}/clients", 
                json=client_data
            )
            
            if response.status_code in [201, 409]:
                self.logger.info(f"Client '{client_config.client_id}' created successfully")
                # Get the created client ID
                client = self.get_client_by_id(client_config.client_id)
                return client.get('id') if client else None
            else:
                self.logger.error(f"Failed to create client: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create client: {e}")
            return None
    
    def _build_client_data(self, config: ClientConfig) -> Dict[str, Any]:
        """Build client data for API request."""
        current_time = int(time.time())
        
        return {
            "clientId": config.client_id,
            "name": config.name,
            "description": config.description,
            "rootUrl": config.root_url,
            "adminUrl": config.root_url,
            "baseUrl": config.root_url,
            "surrogateAuthRequired": False,
            "enabled": True,
            "alwaysDisplayInConsole": False,
            "clientAuthenticatorType": "client-secret",
            "secret": config.secret,
            "redirectUris": config.redirect_uris,
            "webOrigins": config.web_origins,
            "notBefore": 0,
            "bearerOnly": False,
            "consentRequired": False,
            "standardFlowEnabled": True,
            "implicitFlowEnabled": False,
            "directAccessGrantsEnabled": False,
            "serviceAccountsEnabled": False,
            "publicClient": False,
            "frontchannelLogout": False,
            "protocol": "openid-connect",
            "attributes": {
                "realm_client": "false",
                "oidc.ciba.grant.enabled": "false",
                "client.secret.creation.time": str(current_time),
                "backchannel.logout.session.required": "false",
                "standard.token.exchange.enabled": "false",
                "frontchannel.logout.session.required": "true",
                "oauth2.device.authorization.grant.enabled": "false",
                "display.on.consent.screen": "false",
                "backchannel.logout.revoke.offline.tokens": "false"
            },
            "authenticationFlowBindingOverrides": {},
            "fullScopeAllowed": False,
            "nodeReRegistrationTimeout": -1,
            "protocolMappers": config.protocol_mappers,
            "defaultClientScopes": config.default_client_scopes or [
                "web-origins", "acr", "profile", "roles", "basic", "email"
            ],
            "optionalClientScopes": config.optional_client_scopes or [
                "address", "phone", "organization", "offline_access", "microprofile-jwt"
            ]
        }


# =============================================================================
# KEYCLOAK LDAP MANAGER
# =============================================================================

class KeycloakLDAPManager:
    """Manager for Keycloak LDAP federation operations."""
    
    def __init__(self, session: KeycloakAPISession, realm_name: str, logger: logging.Logger):
        self.session = session
        self.realm_name = realm_name
        self.logger = logger
    
    def ldap_federation_exists(self) -> bool:
        """Check if LDAP federation exists."""
        federations = self.get_user_federations()
        return any(fed.get('providerId') == 'ldap' for fed in federations)
    
    def get_user_federations(self) -> List[Dict[str, Any]]:
        """Get all user federations."""
        try:
            response = self.session.request(
                "GET", 
                f"admin/realms/{self.realm_name}/components?type=org.keycloak.storage.UserStorageProvider"
            )
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            self.logger.error(f"Failed to get user federations: {e}")
            return []
    
    def get_ldap_federation_id(self) -> Optional[str]:
        """Get LDAP federation ID."""
        federations = self.get_user_federations()
        for federation in federations:
            if federation.get('providerId') == 'ldap':
                return federation.get('id')
        return None
    
    def create_ldap_federation(self, ldap_config: LDAPConfig, realm_id: str) -> Optional[str]:
        """Create LDAP federation."""
        if self.ldap_federation_exists():
            self.logger.info("LDAP federation already exists")
            return self.get_ldap_federation_id()
        
        federation_data = self._build_ldap_federation_data(ldap_config, realm_id)
        
        try:
            response = self.session.request(
                "POST", 
                f"admin/realms/{self.realm_name}/components", 
                json=federation_data
            )
            
            if response.status_code in [201, 409]:
                self.logger.info("LDAP federation created successfully")
                return self.get_ldap_federation_id()
            else:
                self.logger.error(f"Failed to create LDAP federation: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create LDAP federation: {e}")
            return None
    
    def _build_ldap_federation_data(self, config: LDAPConfig, realm_id: str) -> Dict[str, Any]:
        """Build LDAP federation data."""
        return {
            "name": "ldap",
            "providerId": "ldap",
            "providerType": "org.keycloak.storage.UserStorageProvider",
            "parentId": realm_id,
            "config": {
                "fullSyncPeriod": ["3600"],
                "pagination": ["false"],
                "connectionTrace": ["false"],
                "startTls": ["false"],
                "usersDn": [config.users_dn],
                "connectionPooling": ["true"],
                "cachePolicy": ["DEFAULT"],
                "useKerberosForPasswordAuthentication": ["false"],
                "importEnabled": ["true"],
                "enabled": ["true"],
                "usernameLDAPAttribute": [config.username_attribute],
                "bindDn": [config.bind_dn],
                "bindCredential": [config.bind_credential],
                "changedSyncPeriod": ["3600"],
                "vendor": ["other"],
                "uuidLDAPAttribute": [config.uuid_attribute],
                "allowKerberosAuthentication": ["false"],
                "connectionUrl": [config.connection_url],
                "syncRegistrations": ["true"],
                "authType": ["simple"],
                "krbPrincipalAttribute": ["krb5PrincipalName"],
                "searchScope": ["2"],
                "useTruststoreSpi": ["always"],
                "usePasswordModifyExtendedOp": ["false"],
                "trustEmail": ["false"],
                "userObjectClasses": [config.user_object_classes],
                "removeInvalidUsersEnabled": ["true"],
                "rdnLDAPAttribute": [config.rdn_attribute],
                "editMode": ["READ_ONLY"],
                "validatePasswordPolicy": ["false"]
            }
        }
    
    def sync_ldap_users(self, federation_id: str, sync_type: str = "triggerFullSync") -> bool:
        """Synchronize LDAP users."""
        try:
            response = self.session.request(
                "POST", 
                f"admin/realms/{self.realm_name}/user-storage/{federation_id}/{sync_type}"
            )
            
            if response.status_code in [200, 204]:
                self.logger.info(f"LDAP user sync ({sync_type}) completed successfully")
                return True
            else:
                self.logger.error(f"LDAP sync failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to sync LDAP users: {e}")
            return False


# =============================================================================
# KEYCLOAK ROLE MANAGER
# =============================================================================

class KeycloakRoleManager:
    """Manager for Keycloak role operations."""
    
    def __init__(self, session: KeycloakAPISession, realm_name: str, logger: logging.Logger):
        self.session = session
        self.realm_name = realm_name
        self.logger = logger
    
    def create_client_role(self, client_id: str, role_name: str, description: str = "") -> bool:
        """Create a client role."""
        if self.client_role_exists(client_id, role_name):
            self.logger.info(f"Client role '{role_name}' already exists")
            return True
        
        role_data = {
            "name": role_name,
            "description": description,
            "clientRole": True,
            "composite": False
        }
        
        try:
            response = self.session.request(
                "POST", 
                f"admin/realms/{self.realm_name}/clients/{client_id}/roles", 
                json=role_data
            )
            
            if response.status_code in [201, 409]:
                self.logger.info(f"Client role '{role_name}' created successfully")
                return True
            else:
                self.logger.error(f"Failed to create client role: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to create client role: {e}")
            return False
    
    def client_role_exists(self, client_id: str, role_name: str) -> bool:
        """Check if a client role exists."""
        roles = self.get_client_roles(client_id)
        return any(role.get('name') == role_name for role in roles)
    
    def get_client_roles(self, client_id: str) -> List[Dict[str, Any]]:
        """Get all roles for a client."""
        try:
            response = self.session.request("GET", f"admin/realms/{self.realm_name}/clients/{client_id}/roles")
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            self.logger.error(f"Failed to get client roles: {e}")
            return []
    
    def get_groups(self) -> List[Dict[str, Any]]:
        """Get all groups in the realm."""
        try:
            response = self.session.request("GET", f"admin/realms/{self.realm_name}/groups")
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            self.logger.error(f"Failed to get groups: {e}")
            return []
    
    def get_group_by_name(self, group_name: str) -> Optional[Dict[str, Any]]:
        """Get group by name."""
        groups = self.get_groups()
        for group in groups:
            if group.get('name') == group_name:
                return group
        return None
    
    def map_client_role_to_group(self, client_id: str, role_name: str, group_id: str) -> bool:
        """Map a client role to a group."""
        # Check if mapping already exists
        if self._role_mapping_exists(client_id, role_name, group_id):
            self.logger.info(f"Role '{role_name}' is already mapped to group")
            return True
        
        # Get role data
        roles = self.get_client_roles(client_id)
        role_data = next((role for role in roles if role.get('name') == role_name), None)
        
        if not role_data:
            self.logger.error(f"Role '{role_name}' not found")
            return False
        
        try:
            response = self.session.request(
                "POST", 
                f"admin/realms/{self.realm_name}/groups/{group_id}/role-mappings/clients/{client_id}",
                json=[role_data]
            )
            
            if response.status_code in [200, 204, 409]:
                self.logger.info(f"Role '{role_name}' mapped to group successfully")
                return True
            else:
                self.logger.error(f"Failed to map role to group: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to map role to group: {e}")
            return False
    
    def _role_mapping_exists(self, client_id: str, role_name: str, group_id: str) -> bool:
        """Check if role mapping exists."""
        try:
            response = self.session.request(
                "GET", 
                f"admin/realms/{self.realm_name}/groups/{group_id}/role-mappings/clients/{client_id}"
            )
            
            if response.status_code == 200:
                mapped_roles = response.json()
                return any(role.get('name') == role_name for role in mapped_roles)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to check role mapping: {e}")
            return False


# =============================================================================
# MAIN KEYCLOAK SETUP CLASS
# =============================================================================

class TenantKeycloakSetup:
    """
    Handles Keycloak setup for a new tenant in the LEOS360 platform.

    This class manages the complete Keycloak setup process including:
    - Realm creation and configuration
    - LDAP federation setup and synchronization
    - Client creation (Nextcloud and LEOS360 Portal)
    - Role management and group mapping
    - Authentication flow configuration
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Keycloak setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If required files are missing
        """
        # Validate and normalize customer name
        customer_name = customer_name.strip().lower()
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name

        # Setup logging
        self.logger = setup_logging(customer_name, "keycloak_setup")
        self.logger.info(f"Initializing Keycloak setup for: {customer_name}")

        # File paths
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"

        # Validate customer directory exists
        if not self.customer_dir.exists():
            raise ConfigurationError(f"Customer directory does not exist: {self.customer_dir}")

        # Configuration (will be loaded later)
        self.config: Optional[KeycloakConfig] = None
        self.status = SetupStatus()
        
        # Managers (will be initialized after configuration)
        self.session: Optional[KeycloakAPISession] = None
        self.realm_manager: Optional[KeycloakRealmManager] = None
        self.client_manager: Optional[KeycloakClientManager] = None
        self.ldap_manager: Optional[KeycloakLDAPManager] = None
        self.role_manager: Optional[KeycloakRoleManager] = None

    # =========================================================================
    # CONFIGURATION METHODS
    # =========================================================================

    def load_configuration(self) -> None:
        """Load configuration from environment file with validation."""
        self.logger.info(f"Loading configuration from: {self.env_file}")

        try:
            # Use common environment loading function
            env_vars = load_env_file(self.env_file)

            # Check for required variables using common function
            missing_vars = []
            for var in REQUIRED_ENV_VARS:
                try:
                    get_env_variable(env_vars, var, required=True)
                except:
                    missing_vars.append(var)

            if missing_vars:
                raise ConfigurationError(f"Missing required environment variables: {missing_vars}")

            # Get and validate hostname
            hostname = get_env_variable(env_vars, "KC_HOSTNAME", required=True).rstrip('/')
            if not validate_hostname(hostname):
                raise ConfigurationError(f"Invalid KC_HOSTNAME format: {hostname}")

            # Get and validate realm name
            realm = get_env_variable(env_vars, "KEYCLOAK_REALM", required=True)
            if not validate_realm_name(realm):
                raise ConfigurationError(f"Invalid KEYCLOAK_REALM format: {realm}")

            # Build configuration using common environment functions
            self.config = KeycloakConfig(
                hostname=hostname,
                admin_username=get_env_variable(env_vars, "KEYCLOAK_ADMIN_USERNAME", required=True),
                admin_password=get_env_variable(env_vars, "KEYCLOAK_ADMIN_PASSWORD", required=True),
                realm=realm,
                customer_domain=get_env_variable(env_vars, "CUSTOMER_DOMAIN", required=True),
                base_dn=get_env_variable(env_vars, "LLDAP_BASE_DN", required=True),
                ldap_ro_pass=get_env_variable(env_vars, "LLDAP_RO_PASS", required=True),
                customer_ip=get_env_variable(env_vars, "CUSTOMER_IP", required=True),
                customer_name=get_env_variable(env_vars, "CUSTOMER_NAME", required=False, default=self.customer_name),
                base_domain=get_env_variable(env_vars, "BASE_DOMAIN", required=False, default="leos360.cloud"),
                nextcloud_client_secret=get_env_variable(env_vars, "NEXTCLOUD_KEYCLOAK_CLIENT_SECRET", required=True),
                leos360portal_client_secret=get_env_variable(env_vars, "LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET", required=True),
                verify_ssl=get_env_variable(env_vars, "KEYCLOAK_VERIFY_SSL", required=False, default="true").lower() == "true"
            )

            self.logger.info("Configuration loaded successfully")

        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"Failed to load configuration: {e}")

    def _initialize_managers(self) -> None:
        """Initialize API session and managers."""
        if not self.config:
            raise ConfigurationError("Configuration not loaded")

        # Disable SSL warnings if SSL verification is disabled
        if not self.config.verify_ssl:
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            self.logger.warning("SSL verification is disabled")

        # Initialize API session
        self.session = KeycloakAPISession(self.config)
        
        # Test authentication
        try:
            self.session.authenticate()
            self.logger.info("Keycloak authentication successful")
        except AuthenticationError as e:
            raise AuthenticationError(f"Failed to authenticate with Keycloak: {e}")

        # Initialize managers
        self.realm_manager = KeycloakRealmManager(self.session, self.logger)
        self.client_manager = KeycloakClientManager(self.session, self.config.realm, self.logger)
        self.ldap_manager = KeycloakLDAPManager(self.session, self.config.realm, self.logger)
        self.role_manager = KeycloakRoleManager(self.session, self.config.realm, self.logger)

    # =========================================================================
    # SETUP METHODS
    # =========================================================================

    def setup_realm(self) -> bool:
        """Setup Keycloak realm."""
        print_step("Setting up Keycloak realm...")
        
        try:
            success = self.realm_manager.create_realm(self.config.realm)
            if success:
                self.status.realm_created = True
                print_success(f"Realm '{self.config.realm}' is ready")
            else:
                print_error(f"Failed to create realm '{self.config.realm}'")
            
            return success
            
        except Exception as e:
            print_error(f"Failed to setup realm: {e}")
            self.logger.error(f"Failed to setup realm: {e}", exc_info=True)
            return False

    def setup_ldap_federation(self) -> bool:
        """Setup LDAP federation and mappers."""
        print_step("Setting up LDAP federation...")
        
        try:
            # Get realm info for parent ID
            realm_info = self.realm_manager.get_realm_info(self.config.realm)
            if not realm_info:
                print_error("Failed to get realm information")
                return False
            
            realm_id = realm_info.get('id')
            if not realm_id:
                print_error("Failed to get realm ID")
                return False

            # Build LDAP configuration
            ldap_config = LDAPConfig(
                connection_url=f"ldap://{self.config.customer_ip}:{LDAP_PORT}",
                bind_dn=f"uid=keycloak_ro,ou=people,{self.config.base_dn}",
                bind_credential=self.config.ldap_ro_pass,
                users_dn=f"ou=people,{self.config.base_dn}",
                base_dn=self.config.base_dn
            )

            # Create LDAP federation
            federation_id = self.ldap_manager.create_ldap_federation(ldap_config, realm_id)
            
            if federation_id:
                self.status.ldap_federation_created = True
                print_success("LDAP federation is ready")
                
                # Setup mappers and sync
                if self._setup_ldap_mappers(federation_id):
                    self.status.ldap_mappers_configured = True
                    print_success("LDAP mappers configured")
                
                if self._sync_ldap_data(federation_id):
                    self.status.ldap_synced = True
                    print_success("LDAP data synchronized")
                
                return True
            else:
                print_error("Failed to create LDAP federation")
                return False
                
        except Exception as e:
            print_error(f"Failed to setup LDAP federation: {e}")
            self.logger.error(f"Failed to setup LDAP federation: {e}", exc_info=True)
            return False

    def _setup_ldap_mappers(self, federation_id: str) -> bool:
        """Setup LDAP mappers."""
        print_step("Configuring LDAP mappers...")
        
        # This would implement mapper creation logic
        # For now, we'll assume mappers are configured via the federation setup
        return True

    def _sync_ldap_data(self, federation_id: str) -> bool:
        """Synchronize LDAP data."""
        print_step("Synchronizing LDAP data...")
        
        try:
            # Sync users
            if self.ldap_manager.sync_ldap_users(federation_id):
                print_success("LDAP users synchronized")
                
                # Wait a moment for sync to complete
                time.sleep(3)
                return True
            else:
                print_error("Failed to sync LDAP users")
                return False
                
        except Exception as e:
            print_error(f"Failed to sync LDAP data: {e}")
            self.logger.error(f"Failed to sync LDAP data: {e}", exc_info=True)
            return False

    def setup_clients(self) -> bool:
        """Setup Keycloak clients."""
        print_step("Setting up Keycloak clients...")
        
        success = True
        
        # Setup Nextcloud client
        if self._setup_nextcloud_client():
            self.status.nextcloud_client_created = True
            print_success("Nextcloud client is ready")
        else:
            success = False
            print_error("Failed to setup Nextcloud client")

        # Setup LEOS360 Portal client  
        if self._setup_portal_client():
            self.status.portal_client_created = True
            print_success("LEOS360 Portal client is ready")
        else:
            success = False
            print_error("Failed to setup LEOS360 Portal client")

        return success

    def _setup_nextcloud_client(self) -> bool:
        """Setup Nextcloud client."""
        try:
            client_config = ClientConfig(
                client_id="nextcloud",
                name="nextcloud",
                description="Nextcloud integration client",
                root_url=f"https://{self.config.customer_domain}",
                redirect_uris=[f"https://{self.config.customer_domain}/*"],
                web_origins=[f"https://{self.config.customer_domain}"],
                secret=self.config.nextcloud_client_secret,
                protocol_mappers=self._get_nextcloud_protocol_mappers(),
            )
            
            client_id = self.client_manager.create_client(client_config)
            return client_id is not None
            
        except Exception as e:
            self.logger.error(f"Failed to setup Nextcloud client: {e}", exc_info=True)
            return False

    def _setup_portal_client(self) -> bool:
        """Setup LEOS360 Portal client."""
        try:
            # Get portal URL from environment
            portal_url = os.getenv("leos360sso_url", f"https://{self.config.customer_name}-sso.{self.config.base_domain}")
            
            client_config = ClientConfig(
                client_id="leos360portal",
                name="leos360portal", 
                description="LEOS360 Portal integration client",
                root_url=portal_url,
                redirect_uris=[f"{portal_url}/*"],
                web_origins=[portal_url],
                secret=self.config.leos360portal_client_secret,
                protocol_mappers=self._get_portal_protocol_mappers(),
            )
            
            client_id = self.client_manager.create_client(client_config)
            return client_id is not None
            
        except Exception as e:
            self.logger.error(f"Failed to setup portal client: {e}", exc_info=True)
            return False

    def _get_nextcloud_protocol_mappers(self) -> List[Dict[str, Any]]:
        """Get protocol mappers for Nextcloud client."""
        return [
            {
                "name": "client roles",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-usermodel-client-role-mapper",
                "consentRequired": False,
                "config": {
                    "introspection.token.claim": "true",
                    "multivalued": "true",
                    "userinfo.token.claim": "true",
                    "user.attribute": "foo",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "access.token.claim": "true",
                    "claim.name": "resource_access.${client_id}.roles",
                    "jsonType.label": "String"
                }
            },
            {
                "name": "aud-nextcloud",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-audience-mapper",
                "consentRequired": False,
                "config": {
                    "included.client.audience": "nextcloud",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "introspection.token.claim": "true",
                    "access.token.claim": "true",
                    "userinfo.token.claim": "false"
                }
            }
        ]

    def _get_portal_protocol_mappers(self) -> List[Dict[str, Any]]:
        """Get protocol mappers for portal client."""
        return [
            {
                "name": "client roles",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-usermodel-client-role-mapper",
                "consentRequired": False,
                "config": {
                    "introspection.token.claim": "true",
                    "multivalued": "true",
                    "userinfo.token.claim": "true",
                    "user.attribute": "foo",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "access.token.claim": "true",
                    "claim.name": "resource_access.${client_id}.roles",
                    "jsonType.label": "String"
                }
            },
            {
                "name": "aud-leos360portal",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-audience-mapper",
                "consentRequired": False,
                "config": {
                    "included.client.audience": "leos360portal",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "introspection.token.claim": "true",
                    "access.token.claim": "true",
                    "userinfo.token.claim": "false"
                }
            }
        ]

    def setup_roles_and_mappings(self) -> bool:
        """Setup client roles and group mappings."""
        print_step("Setting up client roles and group mappings...")
        
        success = True
        
        # Parse group mappings from environment
        group_mappings = self._parse_group_mappings()
        
        # Setup Nextcloud roles
        if self.status.nextcloud_client_created:
            success &= self._setup_client_roles("nextcloud", group_mappings.get("nextcloud", {}))
            if success:
                self.status.nextcloud_roles_created = True

        # Setup Portal roles
        if self.status.portal_client_created:
            success &= self._setup_client_roles("leos360portal", group_mappings.get("leos360portal", {}))
            if success:
                self.status.portal_roles_created = True

        if success:
            self.status.role_mappings_created = True
            print_success("Client roles and mappings configured")
        else:
            print_error("Failed to configure some client roles and mappings")

        return success

    def _parse_group_mappings(self) -> Dict[str, Dict[str, str]]:
        """Parse LDAP group mappings from environment."""
        group_mappings = {}
        
        try:
            # Load environment again to get group mappings
            env_vars = load_env_file(self.env_file)
            group_mappings_str = get_env_variable(env_vars, "LLDAP_GRUPPEN_KC_MAPPING", required=False, default="")

            if group_mappings_str:
                # Format: "nextcloud:nc-users,nc-admins;leos360portal:portal-users,portal-admins"
                client_mappings = group_mappings_str.split(";")

                for mapping in client_mappings:
                    if ":" in mapping:
                        client_name, groups = mapping.split(":", 1)
                        client_name = client_name.strip()

                        if "," in groups:
                            users_group, admins_group = [g.strip() for g in groups.split(",", 1)]
                            group_mappings[client_name] = {
                                "users": users_group,
                                "admins": admins_group
                            }
                            self.logger.info(f"Group mapping for {client_name}: users='{users_group}', admins='{admins_group}'")

        except Exception as e:
            self.logger.warning(f"Failed to parse group mappings: {e}")

        return group_mappings

    def _setup_client_roles(self, client_name: str, group_mapping: Dict[str, str]) -> bool:
        """Setup roles for a specific client."""
        try:
            # Get client
            client = self.client_manager.get_client_by_id(client_name)
            if not client:
                self.logger.error(f"Client '{client_name}' not found")
                return False

            client_id = client.get('id')
            
            # Create roles
            success = True
            success &= self.role_manager.create_client_role(client_id, "users", f"{client_name} users")
            success &= self.role_manager.create_client_role(client_id, "admins", f"{client_name} administrators")
            
            if not success:
                return False

            # Map roles to groups
            users_group = group_mapping.get("users", f"{client_name}-users")
            admins_group = group_mapping.get("admins", f"{client_name}-admins")

            # Get group IDs
            users_group_obj = self.role_manager.get_group_by_name(users_group)
            admins_group_obj = self.role_manager.get_group_by_name(admins_group)

            mapping_success = True
            
            if users_group_obj:
                mapping_success &= self.role_manager.map_client_role_to_group(
                    client_id, "users", users_group_obj.get('id')
                )
            else:
                self.logger.warning(f"Group '{users_group}' not found")

            if admins_group_obj:
                mapping_success &= self.role_manager.map_client_role_to_group(
                    client_id, "admins", admins_group_obj.get('id')
                )
            else:
                self.logger.warning(f"Group '{admins_group}' not found")

            return mapping_success

        except Exception as e:
            self.logger.error(f"Failed to setup roles for {client_name}: {e}", exc_info=True)
            return False

    # =========================================================================
    # STATUS AND VALIDATION METHODS
    # =========================================================================

    def show_status(self) -> bool:
        """Show current status of Keycloak setup."""
        print_step(f"Checking Keycloak setup status for customer: {self.customer_name}")

        try:
            if not self.config:
                self.load_configuration()
                self._initialize_managers()

            print(f"\nKeycloak Setup Status for customer: {self.customer_name}")
            print("=" * 60)

            # Check realm
            realm_exists = self.realm_manager.realm_exists(self.config.realm)
            print(f"✓ Realm: {self.config.realm}" if realm_exists else f"✗ Realm: {self.config.realm} (not found)")

            # Check clients
            nextcloud_exists = self.client_manager.client_exists("nextcloud")
            portal_exists = self.client_manager.client_exists("leos360portal")
            
            print(f"✓ Nextcloud Client" if nextcloud_exists else "✗ Nextcloud Client (not found)")
            print(f"✓ LEOS360 Portal Client" if portal_exists else "✗ LEOS360 Portal Client (not found)")

            # Check LDAP federation
            ldap_exists = self.ldap_manager.ldap_federation_exists()
            print(f"✓ LDAP Federation" if ldap_exists else "✗ LDAP Federation (not found)")

            # Check groups
            groups = self.role_manager.get_groups()
            print(f"\nGroups ({len(groups)}):")
            for group in groups[:10]:  # Show first 10 groups
                print(f"  - {group.get('name', 'Unknown')}")
            
            if len(groups) > 10:
                print(f"  ... and {len(groups) - 10} more")

            return True

        except Exception as e:
            print_error(f"Failed to check status: {e}")
            self.logger.error(f"Status check failed: {e}", exc_info=True)
            return False

    def validate_configuration(self) -> bool:
        """Validate the Keycloak configuration."""
        print_step(f"Validating Keycloak configuration for customer: {self.customer_name}")

        validation_errors = []

        try:
            # Validate configuration loading
            if not self.config:
                self.load_configuration()

            # Test Keycloak connectivity
            try:
                self._initialize_managers()
                print_success("Keycloak connectivity test passed")
            except Exception as e:
                validation_errors.append(f"Keycloak connectivity failed: {e}")

            # Validate LDAP configuration
            ldap_url = f"ldap://{self.config.customer_ip}:{LDAP_PORT}"
            if not self._validate_ldap_connectivity(ldap_url):
                validation_errors.append(f"LDAP connectivity failed: {ldap_url}")

            # Validate client secrets
            if len(self.config.nextcloud_client_secret) < 20:
                validation_errors.append("Nextcloud client secret appears too short")
            
            if len(self.config.leos360portal_client_secret) < 20:
                validation_errors.append("LEOS360 Portal client secret appears too short")

            if validation_errors:
                print_error("Configuration validation failed:")
                for error in validation_errors:
                    print_error(f"  - {error}")
                return False
            else:
                print_success(f"Keycloak configuration for {self.customer_name} is valid")
                return True

        except Exception as e:
            print_error(f"Configuration validation failed: {e}")
            self.logger.error(f"Validation failed: {e}", exc_info=True)
            return False

    def _validate_ldap_connectivity(self, ldap_url: str) -> bool:
        """Validate LDAP connectivity."""
        # This would implement actual LDAP connectivity test
        # For now, we'll just validate the URL format
        try:
            from urllib.parse import urlparse
            parsed = urlparse(ldap_url)
            return parsed.scheme == 'ldap' and parsed.hostname and parsed.port
        except Exception:
            return False

    def sync_ldap_only(self) -> bool:
        """Perform LDAP synchronization only."""
        print_step("Performing LDAP synchronization...")

        try:
            if not self.config:
                self.load_configuration()
                self._initialize_managers()

            # Get LDAP federation ID
            federation_id = self.ldap_manager.get_ldap_federation_id()
            if not federation_id:
                print_error("LDAP federation not found")
                return False

            # Sync LDAP data
            if self._sync_ldap_data(federation_id):
                print_success("LDAP synchronization completed successfully")
                return True
            else:
                print_error("LDAP synchronization failed")
                return False

        except Exception as e:
            print_error(f"LDAP synchronization failed: {e}")
            self.logger.error(f"LDAP sync failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str = "setup") -> bool:
        """
        Execute the Keycloak setup operation.

        Args:
            operation: The operation to perform ('setup', 'status', 'validate', 'sync-only')

        Returns:
            True if operation completed successfully
        """
        print_header(f"LEOS360 Keycloak Setup - {self.customer_name}")
        self.logger.info(f"Starting Keycloak {operation} for {self.customer_name}")

        try:
            # Load configuration
            self.load_configuration()

            # Handle different operations
            if operation == "status":
                return self.show_status()
            elif operation == "validate":
                return self.validate_configuration()
            elif operation == "sync-only":
                return self.sync_ldap_only()
            else:  # default: setup
                return self._execute_full_setup()

        except (ConfigurationError, ValidationError, AuthenticationError) as e:
            print_error(f"Keycloak {operation} failed: {str(e)}")
            self.logger.error(f"Keycloak {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during Keycloak {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during Keycloak {operation}: {str(e)}", exc_info=True)
            return False

    def _execute_full_setup(self) -> bool:
        """Execute the complete Keycloak setup process."""
        print_step("Starting complete Keycloak setup process...")

        try:
            # Initialize managers
            self._initialize_managers()

            # Execute setup steps
            success = True
            success &= self.setup_realm()
            success &= self.setup_ldap_federation()
            success &= self.setup_clients()
            success &= self.setup_roles_and_mappings()

            # Display final status
            self._display_final_status()

            if success:
                print_header("OPERATION COMPLETED SUCCESSFULLY")
                print_success(f"Keycloak setup for {self.customer_name} completed successfully!")
                self.logger.info("Keycloak setup completed successfully")
            else:
                print_error("Keycloak setup completed with errors")
                self.logger.error("Keycloak setup completed with errors")

            return success

        except Exception as e:
            print_error(f"Setup execution failed: {e}")
            self.logger.error(f"Setup execution failed: {e}", exc_info=True)
            return False

    def _display_final_status(self) -> None:
        """Display final status of the setup."""
        print_header("SETUP STATUS")

        status_items = [
            ("Realm Created", self.status.realm_created),
            ("LDAP Federation Created", self.status.ldap_federation_created),
            ("LDAP Mappers Configured", self.status.ldap_mappers_configured),
            ("LDAP Data Synchronized", self.status.ldap_synced),
            ("Nextcloud Client Created", self.status.nextcloud_client_created),
            ("LEOS360 Portal Client Created", self.status.portal_client_created),
            ("Nextcloud Roles Created", self.status.nextcloud_roles_created),
            ("Portal Roles Created", self.status.portal_roles_created),
            ("Role Mappings Created", self.status.role_mappings_created)
        ]

        for item_name, item_status in status_items:
            status_symbol = '✓' if item_status else '✗'
            print(f"  {status_symbol} {item_name}")

        # Summary
        completed_items = sum(1 for _, status in status_items if status)
        total_items = len(status_items)
        print(f"\nCompleted: {completed_items}/{total_items} items")


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute Keycloak setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup Keycloak for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage4_tenant_keycloak.py example-customer
  python3 stage4_tenant_keycloak.py example-customer --status
  python3 stage4_tenant_keycloak.py example-customer --validate
  python3 stage4_tenant_keycloak.py example-customer --sync-only

Requirements:
  - Stage 1, 2, and 3 must be completed successfully
  - Keycloak server must be accessible
  - LLDAP container must be running and configured
  - Environment variables must be properly configured
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match previous stages)'
    )
    
    operation_group = parser.add_mutually_exclusive_group()
    operation_group.add_argument(
        '--status',
        action='store_true',
        help='Show status of the Keycloak setup'
    )
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate the Keycloak configuration'
    )
    operation_group.add_argument(
        '--sync-only',
        action='store_true',
        help='Perform LDAP synchronization only'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    if args.status:
        operation = "status"
    elif args.validate:
        operation = "validate"
    elif args.sync_only:
        operation = "sync-only"
    else:
        operation = "setup"

    try:
        # Create and run Keycloak setup
        setup = TenantKeycloakSetup(args.customer_name)
        success = setup.run(operation)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Keycloak setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()