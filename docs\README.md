# LEOS360 Platform - Tenant Setup Scripts

This repository contains the automated tenant setup scripts for the LEOS360 platform. These scripts handle the complete lifecycle of tenant provisioning, from initial configuration to full deployment.

## Overview

The LEOS360 platform uses a multi-stage approach for tenant setup, with each stage handling specific aspects of the provisioning process:

- **Stage 1**: Tenant Configuration Setup
- **Stage 2**: Database, DNS and Web Proxy Setup
- **Stage 3**: Docker Deployment
- **Stage 4**: Service Configuration after Docker Deployment: (Keycloak for Tenant, LLDAP for Tenant, Portal for global Login, Nextcloud for Tenant)

## Prerequisites

### System Requirements
- Linux-based system with root access
- Python 3.12 or higher
- Docker and Docker Compose
- PostgreSQL client tools
- Network access to required services

### Required Directories and Files
```
/mnt/storage/setup/          # Master configuration templates
├── .env                     # Environment template
├── docker-compose.yml       # Docker Compose template
├── ssl/                     # SSL certificates
├── db/
│   └── db_setup.sql.template
├── dovecot/
│   ├── dovecot.conf
│   ├── dovecot-ldap-userdb.conf
│   ├── dovecot-ldap-passdb.conf
│   └── ...
├── postfix/
│   └── ldap/
├── keycloak/
├── nextcloud/
└── lldap/
    └── bootstrap/

/mnt/storage/tenants/        # Tenant configurations
```

## Stage Scripts

### Stage 1: Tenant Configuration Setup

**File**: `stage1_tenant_config.py`

**Purpose**: Creates the initial configuration structure for a new tenant.

**Features**:
- Validates prerequisites and system requirements
- Allocates unique IP addresses and Redis database indices
- Generates secure passwords for all services
- Creates directory structure for all services
- Sets up configuration files with proper variable substitution
- Copies SSL certificates and templates

**Usage**:
```bash
sudo python3 stage1_tenant_config.py <customer_name>
```

**Example**:
```bash
sudo python3 stage1_tenant_config.py example-customer
```

**Output**:
- Creates `/mnt/storage/tenants/<customer_name>/` directory structure
- Generates `.env` file with all configuration variables
- Creates `.secrets/credentials.txt` with generated passwords
- Sets up service-specific configuration files

### Stage 2: Database and Infrastructure Setup

**Files**:
- `stage2_tenant_db.py` - Database setup
- `stage2_tenant_dns.py` - DNS configuration
- `stage2_tenant_webproxy.py` - Web proxy setup

**Purpose**: Sets up external infrastructure components.

**Database Setup**:
```bash
python3 stage2_tenant_db.py <customer_name>
```

**DNS Setup**:
```bash
python3 stage2_tenant_dns.py <customer_name>
```

### Stage 3: Docker Deployment

**File**: `stage3_tenant_docker.py`

**Purpose**: Deploys the Docker stack for the tenant with intelligent Keycloak startup monitoring.

**Features**:
- Deploys or updates Docker Compose stacks via Portainer API
- Monitors Keycloak container logs for successful startup
- Waits for specific startup message before proceeding to Stage 4
- Comprehensive container health monitoring
- Automatic rollback on deployment failures

**Usage**:
```bash
# Standard deployment with Keycloak monitoring
python3 stage3_tenant_docker.py <customer_name>

# Skip Keycloak startup verification
python3 stage3_tenant_docker.py <customer_name> --skip-keycloak-check

# Check deployment status
python3 stage3_tenant_docker.py <customer_name> --status

# Remove deployment
python3 stage3_tenant_docker.py <customer_name> --delete
```

**Keycloak Monitoring**:
The script monitors Keycloak container logs for the following startup indicators:
- `"Keycloak"` - Service identification
- `"started in"` - Startup completion confirmation
- `"Listening on: http://0.0.0.0:8080"` - Service ready indicator
- `"Profile prod activated"` - Production profile confirmation

**Timeout**: 5 minutes (configurable via `KEYCLOAK_STARTUP_TIMEOUT`)

### Stage 4: Service Configuration

**Files**:
- `stage4_tenant_keycloak.py` - Keycloak realm and client setup
- `stage4_tenant_lldap.py` - LLDAP user directory setup
- `stage4_tenant_portal.py` - Portal integration setup

**Purpose**: Configures individual services after deployment.

## Configuration Structure

Each tenant gets a dedicated directory structure:

```
/mnt/storage/tenants/<customer_name>/
├── .env                     # Environment variables
├── .secrets/
│   └── credentials.txt      # Generated passwords
├── docker-compose.yml       # Docker Compose configuration
├── ssl/                     # SSL certificates
├── nextcloud/
│   ├── data/
│   ├── config/
│   └── setup/
├── keycloak/
│   ├── data/
│   └── config/
├── lldap/
│   ├── data/
│   └── config/
├── dovecot/
│   ├── data/
│   └── config/
├── postfix/
│   ├── data/
│   └── config/
├── db/
│   └── db_setup_<customer_name>.sql
└── redis/
    └── data/
```

## Docker Container Health Monitoring

The platform includes comprehensive health monitoring for all Docker containers:

### Container Healthchecks

**Network Container**:
- Verifies IP address assignment (`${CUSTOMER_IP}`)
- Tests internet connectivity (ping to *******)
- Start period: 30 seconds

**LLDAP (User Directory)**:
- Native LLDAP healthcheck command
- Port availability checks:
  - Web API: 17170
  - LDAP: 3890
  - LDAPS: 6360
- Start period: 90 seconds

**Keycloak (SSO)**:
- Java-based HTTP healthcheck
- Endpoint: `http://localhost:9001/health/ready`
- Validates JSON response with `"status":"UP"`
- Start period: 120 seconds

**Postfix (SMTP)**:
- TCP port 25 connectivity
- SMTP banner response validation (220 code)
- Start period: 60 seconds

**Dovecot (IMAP/POP3)**:
- IMAP port 143 connectivity
- IMAPS port 993 connectivity
- Start period: 60 seconds

**Redis Cache**:
- Built-in Redis ping command
- Start period: 30 seconds

**Nextcloud**:
- HTTP status endpoint check
- Start period: 60 seconds

### Health Status Monitoring

All containers report their health status to Docker/Portainer:
- **healthy** - All checks passing
- **unhealthy** - One or more checks failing
- **starting** - Within start period, checks not yet active

## Network Configuration

- **IP Range**: *********** - ************
- **Base Domain**: leos360.cloud
- **Customer Domain**: `<customer_name>.leos360.cloud`
- **Database Host**: ************:5432

## Security Features

- Secure password generation using Python's `secrets` module
- Proper file permissions (644 for files, 755 for directories, 640 for secrets)
- Isolated tenant configurations
- SSL certificate management
- Database user isolation

## Error Handling

All scripts include comprehensive error handling:
- Prerequisite validation
- Rollback on failure
- Detailed logging and status messages
- Cleanup procedures

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure scripts are run with root privileges
2. **Template Not Found**: Verify master templates exist in `/mnt/storage/setup/`
3. **IP Address Exhaustion**: Check available IP range (***********-150)
4. **Database Connection**: Verify PostgreSQL connectivity to ************
5. **Keycloak Startup Timeout**: If Keycloak takes longer than 5 minutes to start, use `--skip-keycloak-check`
6. **Container Health Issues**: Check individual container logs with `docker logs <container_name>`
7. **Portainer API Issues**: Verify Portainer API credentials in `/mnt/storage/docker/.env`

### Health Monitoring Troubleshooting

**Container Status Check**:
```bash
# Check all container health status
docker ps --format "table {{.Names}}\t{{.Status}}"

# Check specific container health details
docker inspect <container_name> | grep -A 10 "Health"

# View container logs
docker logs <container_name> --tail 50
```

**Keycloak Startup Monitoring**:
```bash
# Monitor Keycloak logs in real-time
docker logs <customer_name>-keycloak --follow

# Check for specific startup message
docker logs <customer_name>-keycloak | grep "started in"
```

**Manual Health Check**:
```bash
# Test Keycloak health endpoint
curl http://<customer_ip>:9001/health/ready

# Test LLDAP health
docker exec <customer_name>-lldap /app/lldap healthcheck --config-file /data/lldap_config.toml
```

### Log Messages

Scripts use standardized logging:
- `[INFO]` - General information
- `[SUCCESS]` - Successful operations
- `[WARNING]` - Non-critical issues
- `[ERROR]` - Critical errors requiring attention

## Development Guidelines
Core Python Requirements:
requests - Used extensively across multiple stages for API calls:
stage2_tenant_dns.py - PowerDNS API calls
stage2_tenant_webproxy.py - Web proxy API calls
stage3_tenant_docker.py - Portainer API calls
stage4_tenant_keycloak.py - Keycloak API calls
stage4_tenant_lldap.py - Portainer API calls
stage4_tenant_portal.py - Portal SSO API calls

psycopg2 - PostgreSQL database connectivity:
stage2_tenant_db.py - Database setup and management

python-dotenv - Environment variable management:
stage2_tenant_db.py - Loading .env files
stage2_tenant_dns.py - Environment configuration
stage3_tenant_docker.py - Docker configuration
stage4_tenant_keycloak.py - Keycloak configuration
stage4_tenant_lldap.py - LLDAP configuration
stage4_tenant_portal.py - Portal configuration

urllib3 - HTTP client library (used for SSL warning suppression):
stage3_tenant_docker.py - Portainer API calls
stage4_tenant_lldap.py - Portainer API calls

PyYAML - YAML processing library:
stage3_tenant_docker.py - Docker Compose file validation

### Modernization Recommendations

The codebase currently uses stable, well-tested libraries. However, for future development, consider these modern alternatives:

**Database Connectivity:**
- Current: `psycopg2-binary>=2.9.7`
- Recommended: `psycopg[binary]>=3.1.0` (2x faster, async support, better type hints)
- Migration effort: Medium (some API changes required)

**Data Validation:**
- Current: Manual regex patterns and validation functions
- Recommended: `pydantic>=2.5.0` (structured models, automatic validation, better error messages)
- Benefits: Type safety, JSON schema generation, reduced validation code

**Retry Mechanisms:**
- Current: Custom retry decorators
- Recommended: `tenacity>=8.2.0` (exponential backoff, conditional retries, better logging)
- Benefits: More sophisticated retry strategies, async support

**Structured Logging:**
- Current: Standard Python logging
- Optional: `structlog>=23.2.0` (structured logs, better analysis capabilities)

See `requirements.txt` for detailed upgrade paths and migration guides.

### Code Structure Template

All stage scripts should follow this structure:

**Compliance Status:**
- ✅ **Fully Compliant**: `stage1_tenant_config.py`, `stage2_tenant_db.py`, `setup_tenant_complete.py`
- ⚠️ **Mostly Compliant**: `stage3_tenant_docker.py`, `stage4_tenant_keycloak.py` (additional sections but follows core structure)
- ℹ️ **Library File**: `leos360_common.py` (follows different structure as utility library)

```python
#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage X: Description
=====================================

Brief description of what this stage does.

Author: LEOS360 Development Team
Version: X.X
Last Updated: YYYY-MM-DD
"""

# Imports
import os
import sys
# ... other imports

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

SCRIPT_VERSION = "X.X"
SCRIPT_NAME = "Stage X: Description"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

# ... other utility functions

# =============================================================================
# MAIN CLASS
# =============================================================================

class StageXSetup:
    """Main class for Stage X operations."""

    def __init__(self, customer_name: str) -> None:
        """Initialize setup."""
        pass

    def run(self) -> bool:
        """Main execution method."""
        pass

# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """Main function."""
    pass

if __name__ == "__main__":
    main()
```

### Best Practices

1. **Consistent Naming**: Use clear, descriptive variable and function names
2. **Type Hints**: Include type hints for all function parameters and returns
3. **Documentation**: Comprehensive docstrings for all classes and methods
4. **Error Handling**: Proper exception handling with cleanup procedures
5. **Logging**: Use standardized print functions for consistent output
6. **Validation**: Validate all inputs and prerequisites
7. **Permissions**: Set appropriate file and directory permissions

### Section Organization

Each script should be organized into clearly marked sections:

1. **File Header**: Script description, version, and metadata
2. **Constants**: Configuration values and script settings
3. **Utility Functions**: Common helper functions
4. **Main Class**: Core functionality implementation
5. **Entry Point**: Argument parsing and main execution

## Complete Setup Script

For automated tenant provisioning, use the complete setup script that orchestrates all stages:

**File**: `setup_tenant_complete.py`

**Purpose**: Executes all stage scripts in the correct order with comprehensive error handling and rollback capabilities.

**Features**:
- Automated execution of all stages
- Intelligent Keycloak startup monitoring (replaces fixed wait times)
- Comprehensive error handling and rollback
- Progress tracking and detailed logging
- Selective stage skipping
- Cleanup functionality for existing tenants
- Container health status verification

**Usage**:
```bash
# Complete automated setup
python3 setup_tenant_complete.py <customer_name>

# Skip specific stages
python3 setup_tenant_complete.py <customer_name> --skip-stage3

# Cleanup existing tenant
python3 setup_tenant_complete.py <customer_name> --cleanup-only
```

**Examples**:
```bash
# Full setup for new customer
python3 setup_tenant_complete.py example-customer

# Setup without Docker deployment
python3 setup_tenant_complete.py example-customer --skip-stage3

# Remove existing tenant completely
python3 setup_tenant_complete.py example-customer --cleanup-only
```

## Manual Stage Execution

For manual control or troubleshooting, stages can be executed individually:

```bash
# Stage 1: Create configuration (requires root)
sudo python3 stage1_tenant_config.py my-customer

# Stage 2: Set up infrastructure
python3 stage2_tenant_db.py my-customer
python3 stage2_tenant_dns.py my-customer
python3 stage2_tenant_webproxy.py my-customer

# Stage 3: Deploy Docker stack
python3 stage3_tenant_docker.py my-customer

# Stage 4: Configure services
python3 stage4_tenant_lldap.py my-customer
python3 stage4_tenant_keycloak.py my-customer
python3 stage4_tenant_portal.py my-customer
```

## Support

For issues or questions regarding the tenant setup scripts, please contact the LEOS360 development team.

## Version History

- **v2.2** - Enhanced Docker container health monitoring and Keycloak startup verification
  - Added intelligent Keycloak startup monitoring in Stage 3
  - Implemented comprehensive healthchecks for all Docker containers
  - Java-based healthcheck for Keycloak using native HTTP endpoint
  - Enhanced troubleshooting documentation and monitoring tools
  - Removed fixed wait times in favor of intelligent monitoring
- **v2.1** - Updated all stage files with enhanced functionality and improved consistency
- **v2.0** - Complete restructure with improved error handling and documentation
- **v1.x** - Initial implementation

---

**Note**: This documentation reflects the current state of the tenant setup scripts. Always refer to the individual script files for the most up-to-date usage information and requirements.
