#!/usr/bin/env python3.12
"""
LEOS360 Platform - Complete Tenant Setup
========================================

This script orchestrates the complete tenant setup process by executing all stage scripts
in the correct order. It provides comprehensive error handling, rollback capabilities,
and detailed progress tracking for the entire tenant provisioning workflow.

Author: LEOS360 Development Team
Version: 3.0
Last Updated: 2025-6-2

Prerequisites:
- All stage scripts must be present in the same directory
- Must be run with appropriate privileges (root for Stage 1)
- Master configuration templates must exist in /mnt/storage/setup
- All external services (PostgreSQL, PowerDNS, Portainer) must be accessible

Usage:
    python3 setup_tenant_complete.py <customer_name> [options]

Examples:
    # Complete automated setup
    python3 setup_tenant_complete.py example-customer

    # Skip specific stages
    python3 setup_tenant_complete.py example-customer --skip-stage3

    # Cleanup existing tenant
    python3 setup_tenant_complete.py example-customer --cleanup-only

Features:
- Automated execution of all stages in correct order
- Intelligent error handling and rollback capabilities
- Progress tracking and detailed logging
- Selective stage skipping for troubleshooting
- Cleanup functionality for existing tenants
- Container health status verification
- Service URL display after successful setup

Security:
- Proper privilege escalation for root-required operations
- Secure cleanup of sensitive data during rollback
- Validation of all inputs and prerequisites
- Audit logging for all operations

Requirements:
- Python 3.12+
- All stage scripts in same directory
- Access to LEOS360 platform infrastructure
- Appropriate system privileges

Exit Codes:
- 0: Success
- 1: General error or setup failure
"""

import os
import sys
import subprocess
import argparse
import shutil
from pathlib import Path
from time import sleep
from typing import List, Optional

# Import common functions from LEOS360 platform
from leos360_common import (
    # Print functions
    print_header, print_step, print_warning, print_error, print_success,
    # Validation functions
    validate_customer_name,
    # Constants
    TENANTS_BASE_PATH
)

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "3.0"
SCRIPT_NAME = "Complete Tenant Setup"

# Stage configuration
STAGES = {
    1: {
        'name': 'Tenant Configuration Setup',
        'script': 'stage1_tenant_config.py',
        'requires_root': True,
        'description': 'Creates initial configuration structure and generates passwords'
    },
    2: {
        'name': 'Infrastructure Setup',
        'scripts': [
            'stage2_tenant_db.py',
            'stage2_tenant_dns.py', 
            'stage2_tenant_webproxy.py'
        ],
        'requires_root': False,
        'description': 'Sets up database, DNS records, and web proxy configuration',
        'wait_after': 15  # Wait 15 seconds after completion
    },
    3: {
        'name': 'Docker Deployment',
        'script': 'stage3_tenant_docker.py',
        'requires_root': False,
        'description': 'Deploys Docker stack via Portainer API and verifies Keycloak startup'
        # No wait_after needed - stage3 now includes Keycloak startup verification
    },
    4: {
        'name': 'Service Configuration',
        'scripts': [
            'stage4_tenant_lldap.py',
            'stage4_tenant_keycloak.py',
            'stage4_tenant_portal.py'
        ],
        'requires_root': False,
        'description': 'Configures LLDAP, Keycloak, and Portal services'
    }
}

# Cleanup configuration
CLEANUP_COMMANDS = {
    'stage2_tenant_webproxy.py': ['--delete'],
    'stage2_tenant_dns.py': ['--delete'],
    'stage2_tenant_db.py': ['--reset']
}

# File paths
BASE_TENANT_PATH = TENANTS_BASE_PATH  # Use common constant

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def check_script_exists(script_path: Path) -> bool:
    """
    Check if a script file exists and is executable.
    
    Args:
        script_path: Path to the script file
        
    Returns:
        True if script exists and is readable, False otherwise
    """
    if not script_path.exists():
        print_error(f"Script not found: {script_path}")
        return False
        
    if not script_path.is_file():
        print_error(f"Path is not a file: {script_path}")
        return False
        
    return True


def run_command(command: List[str], error_message: str, cwd: Optional[Path] = None) -> bool:
    """
    Run a command and return success status.
    
    Args:
        command: Command and arguments to execute
        error_message: Error message to display on failure
        cwd: Working directory for command execution
        
    Returns:
        True if command succeeded, False otherwise
    """
    try:
        print_step(f"Executing: {' '.join(command)}")
        result = subprocess.run(
            command,
            capture_output=False,
            text=True,
            cwd=cwd
        )
        
        if result.returncode != 0:
            print_error(f"{error_message} (exit code: {result.returncode})")
            return False
            
        return True
        
    except Exception as e:
        print_error(f"{error_message}: {str(e)}")
        return False


# =============================================================================
# MAIN CLASS
# =============================================================================

class CompleteTenantSetup:
    """
    Orchestrates the complete tenant setup process.
    
    This class manages the execution of all stage scripts in the correct order,
    provides error handling and rollback capabilities, and tracks progress
    throughout the entire tenant provisioning workflow.
    """
    
    def __init__(self, customer_name: str, script_dir: Path) -> None:
        """
        Initialize complete tenant setup.
        
        Args:
            customer_name: Name of the customer/tenant
            script_dir: Directory containing stage scripts
        """
        self.customer_name = customer_name
        self.script_dir = script_dir
        self.completed_stages: List[int] = []
        self.skip_stages: List[int] = []
        
        print_step(f"Initializing complete setup for: {customer_name}")
        print_step(f"Script directory: {script_dir}")


    def validate_prerequisites(self) -> bool:
        """
        Validate all prerequisites before starting setup.
        
        Returns:
            True if all prerequisites are met, False otherwise
        """
        print_step("Validating prerequisites...")
        
        # Check customer name
        if not validate_customer_name(self.customer_name):
            return False
            
        # Check if tenant already exists
        tenant_dir = BASE_TENANT_PATH / self.customer_name
        if tenant_dir.exists():
            print_error(f"Tenant directory already exists: {tenant_dir}")
            return False
            
        # Check all required scripts exist
        for stage_num, stage_config in STAGES.items():
            if stage_num in self.skip_stages:
                continue
                
            if 'script' in stage_config:
                script_path = self.script_dir / stage_config['script']
                if not check_script_exists(script_path):
                    return False
            elif 'scripts' in stage_config:
                for script_name in stage_config['scripts']:
                    script_path = self.script_dir / script_name
                    if not check_script_exists(script_path):
                        return False
        
        print_success("All prerequisites validated successfully")
        return True


    def execute_stage(self, stage_num: int) -> bool:
        """
        Execute a specific stage.
        
        Args:
            stage_num: Stage number to execute
            
        Returns:
            True if stage completed successfully, False otherwise
        """
        if stage_num in self.skip_stages:
            print_warning(f"Skipping Stage {stage_num} as requested")
            return True
            
        stage_config = STAGES[stage_num]
        print_header(f"Stage {stage_num}: {stage_config['name']}")
        print_step(stage_config['description'])
        
        # Handle single script
        if 'script' in stage_config:
            script_path = self.script_dir / stage_config['script']
            command = ['python3', str(script_path), self.customer_name]
            
            # Add sudo for root-required stages
            if stage_config.get('requires_root', False):
                command = ['sudo'] + command
                
            if not run_command(command, f"Stage {stage_num} failed"):
                return False
                
        # Handle multiple scripts
        elif 'scripts' in stage_config:
            for script_name in stage_config['scripts']:
                script_path = self.script_dir / script_name
                command = ['python3', str(script_path), self.customer_name]
                
                if not run_command(command, f"Stage {stage_num} script {script_name} failed"):
                    return False
        
        # Wait if specified
        if 'wait_after' in stage_config:
            wait_time = stage_config['wait_after']
            print_step(f"Waiting {wait_time} seconds for services to initialize...")
            sleep(wait_time)
        
        print_success(f"Stage {stage_num} completed successfully")
        self.completed_stages.append(stage_num)
        return True


    def cleanup_stage(self, stage_num: int) -> None:
        """
        Cleanup a specific stage.
        
        Args:
            stage_num: Stage number to cleanup
        """
        stage_config = STAGES[stage_num]
        print_step(f"Cleaning up Stage {stage_num}: {stage_config['name']}")
        
        try:
            if stage_num == 1:
                # Remove tenant directory
                tenant_dir = BASE_TENANT_PATH / self.customer_name
                if tenant_dir.exists():
                    print_step(f"Removing tenant directory: {tenant_dir}")
                    shutil.rmtree(tenant_dir, ignore_errors=True)
                    
            elif stage_num == 2:
                # Cleanup infrastructure components
                for script_name in ['stage2_tenant_webproxy.py', 'stage2_tenant_dns.py', 'stage2_tenant_db.py']:
                    if script_name in CLEANUP_COMMANDS:
                        script_path = self.script_dir / script_name
                        if script_path.exists():
                            command = ['python3', str(script_path), self.customer_name] + CLEANUP_COMMANDS[script_name]
                            run_command(command, f"Cleanup of {script_name} failed", cwd=self.script_dir)
                            
        except Exception as e:
            print_warning(f"Cleanup of Stage {stage_num} failed: {str(e)}")


    def perform_rollback(self) -> None:
        """Perform rollback of completed stages."""
        if not self.completed_stages:
            print_step("No stages to rollback")
            return
            
        print_header("PERFORMING ROLLBACK")
        print_step(f"Rolling back stages: {self.completed_stages}")
        
        # Rollback in reverse order
        for stage_num in reversed(self.completed_stages):
            self.cleanup_stage(stage_num)
            
        print_success("Rollback completed")


    def run(self) -> bool:
        """
        Execute the complete tenant setup process.
        
        Returns:
            True if setup completed successfully, False otherwise
        """
        print_header(f"LEOS360 Complete Tenant Setup - {self.customer_name}")
        print_step(f"Starting complete setup for: {self.customer_name}")
        print_step(f"Version: {SCRIPT_VERSION}")
        
        try:
            # Validate prerequisites
            if not self.validate_prerequisites():
                return False
            
            # Execute all stages
            for stage_num in sorted(STAGES.keys()):
                if not self.execute_stage(stage_num):
                    print_error(f"Setup failed at Stage {stage_num}")
                    self.perform_rollback()
                    return False
            
            # Display service URLs
            self.display_service_urls()
            
            print_header("SETUP COMPLETED SUCCESSFULLY")
            print_success(f"Complete tenant setup for {self.customer_name} finished successfully!")
            return True
            
        except KeyboardInterrupt:
            print_error("Setup interrupted by user")
            self.perform_rollback()
            return False
        except Exception as e:
            print_error(f"Unexpected error during setup: {str(e)}")
            self.perform_rollback()
            return False


    def display_service_urls(self) -> None:
        """Display service URLs for the tenant."""
        print_header("SERVICE URLS")
        print(f"- Nextcloud:     https://{self.customer_name}.leos360.cloud")
        print(f"- Keycloak SSO:  https://{self.customer_name}-sso.leos360.cloud")
        print(f"- LLDAP API:     https://{self.customer_name}-api.leos360.cloud")
        print(f"- LEOS360 Portal: https://portal-dev.leos360.com")


    def cleanup_only(self) -> bool:
        """
        Perform cleanup only (for existing tenant).

        Returns:
            True if cleanup completed successfully, False otherwise
        """
        print_header(f"CLEANUP ONLY - {self.customer_name}")

        # Assume all stages were completed for cleanup
        self.completed_stages = list(STAGES.keys())
        self.perform_rollback()
        return True


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute complete tenant setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Complete tenant setup for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 setup_tenant_complete.py example-customer
  python3 setup_tenant_complete.py example-customer --skip-stage3
  python3 setup_tenant_complete.py example-customer --cleanup-only

Requirements:
  - All stage scripts must be present in the same directory
  - Must be run with appropriate privileges (root for Stage 1)
  - Master configuration templates must exist in /mnt/storage/setup
  - All external services must be accessible
        """
    )

    parser.add_argument(
        'customer_name',
        help='Name of the customer (lowercase letters, numbers, hyphens only)'
    )

    parser.add_argument(
        '--skip-stage1',
        action='store_true',
        help='Skip Stage 1 (Tenant Configuration Setup)'
    )

    parser.add_argument(
        '--skip-stage2',
        action='store_true',
        help='Skip Stage 2 (Infrastructure Setup)'
    )

    parser.add_argument(
        '--skip-stage3',
        action='store_true',
        help='Skip Stage 3 (Docker Deployment)'
    )

    parser.add_argument(
        '--skip-stage4',
        action='store_true',
        help='Skip Stage 4 (Service Configuration)'
    )

    parser.add_argument(
        '--cleanup-only',
        action='store_true',
        help='Perform cleanup only (remove existing tenant)'
    )

    parser.add_argument(
        '--version',
        action='version',
        version=f'{SCRIPT_NAME} v{SCRIPT_VERSION}'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Get script directory
    script_dir = Path(os.path.dirname(os.path.abspath(__file__)))

    try:
        # Create setup instance
        setup = CompleteTenantSetup(args.customer_name, script_dir)

        # Configure skipped stages
        if args.skip_stage1:
            setup.skip_stages.append(1)
        if args.skip_stage2:
            setup.skip_stages.append(2)
        if args.skip_stage3:
            setup.skip_stages.append(3)
        if args.skip_stage4:
            setup.skip_stages.append(4)

        # Execute requested operation
        if args.cleanup_only:
            success = setup.cleanup_only()
        else:
            success = setup.run()

        if not success:
            sys.exit(1)

    except KeyboardInterrupt:
        print_error("Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
