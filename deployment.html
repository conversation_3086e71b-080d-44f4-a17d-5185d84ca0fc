<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LEOS360 Platform - Deployment Architecture</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: auto;
        }

        .app-container {
            min-height: 100vh;
            padding: 20px;
        }

        .main-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 20px;
        }

        .main-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        /* Modern Card Styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 25px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-title i {
            color: #3498db;
        }

        /* Infrastructure Grid */
        .infrastructure-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .server-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 2px solid #e9ecef;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .server-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            border-color: #3498db;
        }

        .server-card.deployment {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
            color: white;
            border-color: #c0392b;
        }

        .server-card.portainer {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
            border-color: #2980b9;
        }

        .server-card.sso {
            background: linear-gradient(145deg, #9b59b6, #8e44ad);
            color: white;
            border-color: #8e44ad;
        }

        .server-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            display: block;
            opacity: 0.9;
        }

        .server-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .server-ip {
            background: rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 8px 12px;
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            margin-bottom: 12px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .server-desc {
            font-size: 0.95rem;
            line-height: 1.5;
            opacity: 0.9;
        }
        
        /* Docker Stack Styles */
        .docker-stack {
            background: linear-gradient(145deg, #34495e, #2c3e50);
            border-radius: 20px;
            padding: 30px;
            color: white;
            margin-bottom: 30px;
        }

        .stack-services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .service-card {
            background: linear-gradient(145deg, #3498db, #2980b9);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: 2px solid transparent;
        }

        .service-card:hover {
            transform: translateY(-5px) scale(1.03);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: rgba(255,255,255,0.3);
        }

        .service-icon {
            font-size: 2.8rem;
            margin-bottom: 15px;
            display: block;
            opacity: 0.9;
        }

        .service-name {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .service-desc {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* Deployment Stages */
        .stages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stage-card {
            border-radius: 16px;
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            color: white;
        }

        .stage-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .stage-1 { background: linear-gradient(145deg, #3498db, #2980b9); }
        .stage-2 { background: linear-gradient(145deg, #e74c3c, #c0392b); }
        .stage-3 { background: linear-gradient(145deg, #f39c12, #e67e22); }
        .stage-4 { background: linear-gradient(145deg, #9b59b6, #8e44ad); }

        .stage-number {
            position: absolute;
            top: 20px;
            right: 25px;
            font-size: 4rem;
            opacity: 0.2;
            font-weight: 700;
        }

        .stage-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-right: 60px;
        }

        .stage-actions {
            list-style: none;
        }

        .stage-actions li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .stage-actions li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: rgba(255,255,255,0.7);
            font-size: 0.8rem;
        }
        
        /* API Flow Styles */
        .api-flows {
            display: grid;
            gap: 20px;
            margin-top: 25px;
        }

        .flow-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .flow-node {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.95rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .flow-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.25);
        }

        .flow-source {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
            color: white;
        }

        .flow-target {
            background: linear-gradient(145deg, #27ae60, #229954);
            color: white;
        }

        .flow-api {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
        }

        .flow-arrow {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.7);
            animation: pulse-arrow 2s infinite;
        }

        @keyframes pulse-arrow {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        @keyframes pulse-glow {
            0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
            70% { box-shadow: 0 0 0 15px rgba(52, 152, 219, 0); }
            100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
        }

        .active-stage {
            animation: pulse-glow 2s infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
            }

            .infrastructure-grid {
                grid-template-columns: 1fr;
            }

            .stages-grid {
                grid-template-columns: 1fr;
            }

            .flow-container {
                flex-direction: column;
                gap: 15px;
            }

            .flow-arrow {
                transform: rotate(90deg);
            }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .mb-4 { margin-bottom: 2rem; }
        .mt-4 { margin-top: 2rem; }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Infrastructure servers data
        const infrastructureServers = [
            {
                id: 'deployment',
                title: 'Deployment Server',
                icon: 'fas fa-bullseye',
                ip: '************',
                description: 'Central API Controller\nOrchestriert alle Deployment-Stages',
                type: 'deployment'
            },
            {
                id: 'portainer',
                title: 'Portainer Server',
                icon: 'fas fa-sliders-h',
                ip: '************',
                description: 'Docker Management API\nSteuert alle Docker Hosts',
                type: 'portainer'
            },
            {
                id: 'sso',
                title: 'SSO/Keycloak Global',
                icon: 'fas fa-shield-alt',
                ip: '************',
                description: 'Globales SSO System\nZentrale Authentifizierung',
                type: 'sso'
            },
            {
                id: 'storage',
                title: 'Storage Server',
                icon: 'fas fa-hdd',
                ip: '************',
                description: 'Stage1 & Tenant Data\nKonfigurationsspeicher',
                type: 'standard'
            },
            {
                id: 'database',
                title: 'DB Cluster',
                icon: 'fas fa-database',
                ip: '************',
                description: 'PostgreSQL für alle Tenants\nZentrale Datenhaltung',
                type: 'standard'
            },
            {
                id: 'dns',
                title: 'DNS Server',
                icon: 'fas fa-globe',
                ip: '************',
                description: 'DNS für alle Tenants & Infra\nDomain-Management',
                type: 'standard'
            },
            {
                id: 'proxy',
                title: 'WebProxy',
                icon: 'fas fa-link',
                ip: '************',
                description: 'Globaler Proxy alle Tenants\nLoad Balancing & Routing',
                type: 'standard'
            },
            {
                id: 'docker',
                title: 'Docker Host',
                icon: 'fab fa-docker',
                ip: '***********',
                description: 'Container für alle Tenants\nMicroservices Orchestrierung',
                type: 'standard'
            }
        ];

        // Docker services data
        const dockerServices = [
            {
                id: 'nextcloud',
                name: 'Nextcloud',
                icon: 'fas fa-cloud',
                description: 'File Sharing & Collaboration',
                details: 'WebDAV, CalDAV, CardDAV\n• Office Integration\n• Benutzer-Management über LLDAP'
            },
            {
                id: 'lldap',
                name: 'LLDAP',
                icon: 'fas fa-address-book',
                description: 'LDAP Directory für Tenant',
                details: 'Benutzer- und Gruppenverwaltung\n• Integration mit Keycloak\n• LDAP-Backend für alle Services'
            },
            {
                id: 'redis',
                name: 'Redis',
                icon: 'fas fa-bolt',
                description: 'In-Memory Cache',
                details: 'Session-Storage\n• Cache für Nextcloud\n• Performance-Optimierung'
            },
            {
                id: 'keycloak',
                name: 'Keycloak Tenant',
                icon: 'fas fa-key',
                description: 'Identity Management',
                details: 'Single Sign-On für Tenant\n• OAuth2, OIDC, SAML\n• Integration mit Global SSO'
            },
            {
                id: 'dovecot',
                name: 'Dovecot',
                icon: 'fas fa-inbox',
                description: 'IMAP/POP3 Mail Server',
                details: 'Mail-Zustellung\n• Sieve-Filter\n• Integration mit LLDAP'
            },
            {
                id: 'postfix',
                name: 'Postfix',
                icon: 'fas fa-paper-plane',
                description: 'SMTP Mail Transfer',
                details: 'Mail-Versand\n• Anti-Spam\n• Domain-Routing'
            }
        ];

        // Deployment stages data
        const deploymentStages = [
            {
                id: 1,
                title: '🔧 Tenant Configuration Setup',
                actions: [
                    'Tenant-Konfiguration erstellen',
                    'Storage Server vorbereiten',
                    'Basis-Parameter definieren',
                    'SSO-Integration planen'
                ]
            },
            {
                id: 2,
                title: '🏗️ Database, DNS & WebProxy Setup',
                actions: [
                    'Database für Tenant einrichten',
                    'DNS-Einträge konfigurieren',
                    'WebProxy-Routing einrichten',
                    'SSO-Verbindung konfigurieren'
                ]
            },
            {
                id: 3,
                title: '🐳 Docker Stack Deployment',
                actions: [
                    'Portainer API: Stack erstellen',
                    'Alle Services deployen',
                    'Container-Netzwerk einrichten',
                    'Health Checks durchführen'
                ]
            },
            {
                id: 4,
                title: '⚙️ Service Configuration',
                actions: [
                    'Keycloak Tenant konfigurieren',
                    'LLDAP mit SSO verknüpfen',
                    'Nextcloud & Mail-Services',
                    'Redis Cache optimieren'
                ]
            }
        ];

        // API flows data
        const apiFlows = [
            {
                id: 1,
                source: 'Deployment Server',
                api: 'Stage 1-2 API',
                target: 'Storage, DB, DNS, Proxy',
                icon: '📡'
            },
            {
                id: 2,
                source: 'Deployment Server',
                api: 'Portainer API',
                target: 'Docker Host Stack',
                icon: '🐳'
            },
            {
                id: 3,
                source: 'Tenant Services',
                api: 'SSO/Keycloak Global',
                target: 'Zentrale Authentifizierung',
                icon: '🔐'
            }
        ];

        // React Components
        const ServerCard = ({ server, onClick }) => (
            <div
                className={`server-card ${server.type}`}
                onClick={() => onClick(server)}
            >
                <i className={`server-icon ${server.icon}`}></i>
                <div className="server-title">{server.title}</div>
                <div className="server-ip">IP: {server.ip}</div>
                <div className="server-desc">{server.description.replace('\n', ' • ')}</div>
            </div>
        );

        const ServiceCard = ({ service, onClick }) => (
            <div
                className="service-card"
                onClick={() => onClick(service)}
            >
                <i className={`service-icon ${service.icon}`}></i>
                <div className="service-name">{service.name}</div>
                <div className="service-desc">{service.description}</div>
            </div>
        );

        const StageCard = ({ stage, isActive, onClick }) => (
            <div
                className={`stage-card stage-${stage.id} ${isActive ? 'active-stage' : ''}`}
                onClick={() => onClick(stage)}
            >
                <div className="stage-number">{stage.id}</div>
                <div className="stage-title">{stage.title}</div>
                <ul className="stage-actions">
                    {stage.actions.map((action, index) => (
                        <li key={index}>{action}</li>
                    ))}
                </ul>
            </div>
        );

        const FlowDiagram = ({ flow }) => (
            <div className="flow-container">
                <div className="flow-node flow-source">{flow.source}</div>
                <div className="flow-arrow">{flow.icon}</div>
                <div className="flow-node flow-api">{flow.api}</div>
                <div className="flow-arrow">→</div>
                <div className="flow-node flow-target">{flow.target}</div>
            </div>
        );

        // Main App Component
        const App = () => {
            const [activeStage, setActiveStage] = useState(1);

            useEffect(() => {
                const interval = setInterval(() => {
                    setActiveStage(prev => prev >= 4 ? 1 : prev + 1);
                }, 4000);

                return () => clearInterval(interval);
            }, []);

            const handleServerClick = (server) => {
                const details = {
                    deployment: 'Zentrale Orchestrierung aller Stages\n• API-Controller für Infrastructure\n• Monitoring & Logging',
                    portainer: 'Docker Container Management\n• Stack Deployment via API\n• Container Health Monitoring',
                    sso: 'Zentrale Authentifizierung\n• Multi-Tenant SSO\n• Identity Federation',
                    storage: 'Tenant-Konfigurationen\n• File Storage für Nextcloud\n• Backup & Archivierung',
                    database: 'PostgreSQL für alle Tenants\n• High Availability\n• Backup & Replication',
                    dns: 'Subdomain-Management\n• Service Discovery\n• Load Balancing Records',
                    proxy: 'Reverse Proxy\n• SSL Termination\n• Traffic Routing',
                    docker: 'Container Runtime\n• Service Orchestrierung\n• Resource Management'
                };
                alert(`${server.title}\n\n${details[server.id] || server.description}`);
            };

            const handleServiceClick = (service) => {
                alert(`${service.name}\n\n${service.details}`);
            };

            const handleStageClick = (stage) => {
                alert(`Stage ${stage.id} Details:\n\n• ${stage.actions.join('\n• ')}`);
            };

            return (
                <div className="app-container">
                    <header className="main-header">
                        <h1 className="main-title">🚀 LEOS360 Platform</h1>
                        <p className="main-subtitle">Multi-Stage Tenant Deployment Infrastructure</p>
                    </header>

                    {/* Infrastructure Section */}
                    <div className="card">
                        <h2 className="section-title">
                            <i className="fas fa-server"></i>
                            Infrastructure Services
                        </h2>
                        <div className="infrastructure-grid">
                            {infrastructureServers.map(server => (
                                <ServerCard
                                    key={server.id}
                                    server={server}
                                    onClick={handleServerClick}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Docker Stack Section */}
                    <div className="card docker-stack">
                        <h2 className="section-title">
                            <i className="fab fa-docker"></i>
                            Docker Stack - Tenant Services
                        </h2>
                        <div className="stack-services">
                            {dockerServices.map(service => (
                                <ServiceCard
                                    key={service.id}
                                    service={service}
                                    onClick={handleServiceClick}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Deployment Stages Section */}
                    <div className="card">
                        <h2 className="section-title">
                            <i className="fas fa-tasks"></i>
                            Deployment Stages Ablauf
                        </h2>
                        <div className="stages-grid">
                            {deploymentStages.map(stage => (
                                <StageCard
                                    key={stage.id}
                                    stage={stage}
                                    isActive={activeStage === stage.id}
                                    onClick={handleStageClick}
                                />
                            ))}
                        </div>
                    </div>

                    {/* API Flow Section */}
                    <div className="card">
                        <h2 className="section-title">
                            <i className="fas fa-exchange-alt"></i>
                            API-Steuerung & Datenfluss
                        </h2>
                        <div className="api-flows">
                            {apiFlows.map(flow => (
                                <FlowDiagram key={flow.id} flow={flow} />
                            ))}
                        </div>
                    </div>
                </div>
            );
        };

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>