#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 2: Web Proxy Setup
===========================================

This script handles the web proxy configuration for a new tenant in the LEOS360 platform.
It configures the reverse proxy to route traffic for the customer domain to the appropriate
backend services using the web proxy API.

Author: LEOS360 Development Team
Version: 3.0
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 must be completed successfully
- Web proxy API must be accessible at ************:5000
- Customer IP must be allocated and available in .env file

Usage:
    python3 stage2_tenant_webproxy.py <customer_name> [options]

Examples:
    python3 stage2_tenant_webproxy.py example-customer
    python3 stage2_tenant_webproxy.py example-customer --ip ***********
    python3 stage2_tenant_webproxy.py example-customer --delete
    python3 stage2_tenant_webproxy.py example-customer --status
"""

import sys
import argparse
import time
import ipaddress
from typing import Optional, Dict, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# Import common functions
from leos360_common import (
    # Exceptions
    LEOS360Error, ConfigurationError, ValidationError,
    # Logging
    setup_logging,
    # Print functions
    print_header, print_step, print_warning, print_error, print_success,
    # Validation
    validate_customer_name,
    # Constants
    TENANTS_BASE_PATH, DEFAULT_API_TIMEOUT, DEFAULT_API_RETRIES,
    DEFAULT_API_BACKOFF_FACTOR
)

# =============================================================================
# WEBPROXY-SPECIFIC EXCEPTIONS
# =============================================================================

class WebProxySetupError(LEOS360Error):
    """Base exception for web proxy setup errors."""
    pass

class APIError(WebProxySetupError):
    """Raised when API operations fail."""
    pass

class EnvironmentError(WebProxySetupError):
    """Raised when environment file issues occur."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "3.0"
SCRIPT_NAME = "Stage 2: Web Proxy Setup"

# Web proxy API configuration
WEBPROXY_API_BASE = "http://************:5000"
WEBPROXY_ADD_ENDPOINT = "/webproxy/add"
WEBPROXY_DELETE_ENDPOINT = "/webproxy/del"
WEBPROXY_STATUS_ENDPOINT = "/webproxy/status"

# Network configuration (use common defaults)
API_TIMEOUT = DEFAULT_API_TIMEOUT
API_RETRIES = DEFAULT_API_RETRIES
API_BACKOFF_FACTOR = DEFAULT_API_BACKOFF_FACTOR
CONNECT_TIMEOUT = 10
READ_TIMEOUT = 20

# File paths
BASE_TENANT_PATH = TENANTS_BASE_PATH
LOG_PERMISSIONS = 0o644

# IP Networks for validation
IP_NETWORKS = [
    ipaddress.IPv4Network('**********/24'),  # Customer network
    ipaddress.IPv4Network('10.0.0.0/8'),     # Internal networks
    ipaddress.IPv4Network('***********/16')   # Private networks
]

# API response codes
API_SUCCESS_CODES = {200, 201}
API_NOT_FOUND_CODES = {404}
API_CLIENT_ERROR_CODES = {400, 401, 403, 422}
API_SERVER_ERROR_CODES = {500, 502, 503, 504}


# =============================================================================
# DATA CLASSES
# =============================================================================

@dataclass
class WebProxyConfig:
    """Configuration for web proxy setup."""
    customer_name: str
    customer_ip: str
    api_base: str
    timeout: int = API_TIMEOUT

@dataclass
class APIResponse:
    """Represents an API response."""
    success: bool
    status_code: int
    data: Optional[Dict[str, Any]] = None
    message: str = ""


# =============================================================================
# WEBPROXY-SPECIFIC UTILITY FUNCTIONS
# =============================================================================


def validate_ip_address(ip_str: str) -> bool:
    """
    Validate IP address format and network membership.
    
    Args:
        ip_str: IP address string to validate
        
    Returns:
        True if valid and in allowed networks
    """
    try:
        ip = ipaddress.IPv4Address(ip_str)
        # Check if IP is in any of the allowed networks
        return any(ip in network for network in IP_NETWORKS)
    except (ipaddress.AddressValueError, ValueError):
        return False


def validate_api_url(url: str) -> bool:
    """
    Validate API URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
    except Exception:
        return False


# =============================================================================
# HTTP SESSION MANAGER
# =============================================================================

class WebProxyAPISession:
    """HTTP session manager with retry logic and proper error handling."""
    
    def __init__(self, api_base: str):
        if not validate_api_url(api_base):
            raise ConfigurationError(f"Invalid API base URL: {api_base}")
            
        self.api_base = api_base.rstrip('/')
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=API_RETRIES,
            backoff_factor=API_BACKOFF_FACTOR,
            status_forcelist=list(API_SERVER_ERROR_CODES) + [429],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "DELETE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": f"LEOS360-WebProxy-Setup/{SCRIPT_VERSION}"
        })
        
        return session
    
    def request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """
        Make an API request with proper error handling.
        
        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            **kwargs: Additional request arguments
            
        Returns:
            APIResponse object
        """
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=(CONNECT_TIMEOUT, READ_TIMEOUT),
                **kwargs
            )
            
            # Parse response data
            response_data = None
            if response.content:
                try:
                    response_data = response.json()
                except ValueError:
                    response_data = {"raw_content": response.text}
            
            # Determine success based on status code
            success = response.status_code in API_SUCCESS_CODES
            
            return APIResponse(
                success=success,
                status_code=response.status_code,
                data=response_data,
                message=self._get_error_message(response)
            )
            
        except requests.exceptions.Timeout:
            return APIResponse(
                success=False,
                status_code=0,
                message=f"Request timed out after {API_TIMEOUT} seconds"
            )
        except requests.exceptions.ConnectionError:
            return APIResponse(
                success=False,
                status_code=0,
                message=f"Connection failed to {url}"
            )
        except requests.exceptions.RequestException as e:
            return APIResponse(
                success=False,
                status_code=0,
                message=f"Request failed: {str(e)}"
            )
    
    def _get_error_message(self, response: requests.Response) -> str:
        """Get appropriate error message based on response."""
        if response.status_code in API_SUCCESS_CODES:
            return "Success"
        elif response.status_code in API_NOT_FOUND_CODES:
            return "Resource not found"
        elif response.status_code in API_CLIENT_ERROR_CODES:
            return f"Client error: {response.text}"
        elif response.status_code in API_SERVER_ERROR_CODES:
            return f"Server error: {response.text}"
        else:
            return f"Unexpected status code {response.status_code}: {response.text}"
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Close the session."""
        self.session.close()


# =============================================================================
# MAIN WEB PROXY SETUP CLASS
# =============================================================================

class TenantWebProxySetup:
    """
    Handles web proxy setup for a new tenant in the LEOS360 platform.

    This class manages the complete web proxy setup process including:
    - Reading customer IP from environment file
    - Configuring reverse proxy routing
    - API communication with web proxy service
    - Error handling and validation
    - Status checking and monitoring
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant web proxy setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If configuration is invalid
        """
        # Validate and normalize customer name
        customer_name = customer_name.strip().lower()
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name
        
        # Setup logging
        self.logger = setup_logging(customer_name)
        self.logger.info(f"Initializing web proxy setup for: {customer_name}")

        # File paths
        self.env_file = BASE_TENANT_PATH / customer_name / ".env"
        
        # Validate that tenant directory exists
        if not (BASE_TENANT_PATH / customer_name).exists():
            raise ConfigurationError(f"Tenant directory does not exist: {BASE_TENANT_PATH / customer_name}")

        # API configuration
        self.api_base = WEBPROXY_API_BASE
        
        # Validate API base URL
        if not validate_api_url(self.api_base):
            raise ConfigurationError(f"Invalid API base URL: {self.api_base}")

        # Runtime variables
        self.customer_ip = ""

    # =========================================================================
    # ENVIRONMENT FILE HANDLING
    # =========================================================================

    def get_customer_ip_from_env(self) -> str:
        """
        Get customer IP from .env file using robust parsing.

        Returns:
            Customer IP address

        Raises:
            EnvironmentError: If .env file issues occur
            ValidationError: If IP is invalid
        """
        if not self.env_file.exists():
            raise EnvironmentError(f"Environment file does not exist: {self.env_file}")

        try:
            # Read environment file manually
            with open(self.env_file, 'r') as f:
                content = f.read()

            # Parse CUSTOMER_IP
            customer_ip = None
            for line in content.splitlines():
                line = line.strip()
                if line.startswith('CUSTOMER_IP='):
                    customer_ip = line.split('=', 1)[1].strip().strip('"\'')
                    break

            if not customer_ip:
                raise EnvironmentError(f"CUSTOMER_IP not found in {self.env_file}")

            # Validate IP address
            if not validate_ip_address(customer_ip):
                raise ValidationError(f"Invalid customer IP address: {customer_ip}")

            self.logger.info(f"Found valid customer IP in environment file: {customer_ip}")
            return customer_ip

        except (IOError, UnicodeDecodeError) as e:
            raise EnvironmentError(f"Could not read environment file {self.env_file}: {str(e)}")

    # =========================================================================
    # API COMMUNICATION METHODS
    # =========================================================================

    def check_api_health(self, session: WebProxyAPISession) -> bool:
        """
        Check if the web proxy API is healthy and accessible.
        
        Args:
            session: API session
            
        Returns:
            True if API is healthy
        """
        try:
            # Try a simple health check (assuming the API has a health endpoint)
            response = session.request("GET", "/health")
            if response.success:
                self.logger.info("Web proxy API is healthy")
                return True
            else:
                # If no health endpoint, try the status endpoint
                response = session.request("GET", WEBPROXY_STATUS_ENDPOINT.lstrip('/'))
                if response.status_code in [200, 404]:  # 404 is OK for status endpoint
                    self.logger.info("Web proxy API is accessible")
                    return True
                    
        except Exception as e:
            self.logger.warning(f"API health check failed: {e}")
        
        return False

    def get_proxy_status(self, session: WebProxyAPISession) -> Optional[Dict[str, Any]]:
        """
        Get current proxy status for the customer.
        
        Args:
            session: API session
            
        Returns:
            Status data or None if not found
        """
        endpoint = f"{WEBPROXY_STATUS_ENDPOINT.lstrip('/')}/{self.customer_name}"
        response = session.request("GET", endpoint)
        
        if response.success:
            return response.data
        elif response.status_code in API_NOT_FOUND_CODES:
            return None
        else:
            raise APIError(f"Failed to get proxy status: {response.message}")

    # =========================================================================
    # WEB PROXY OPERATION METHODS
    # =========================================================================

    def setup_webproxy(self, customer_ip: Optional[str] = None) -> bool:
        """
        Set up web proxy configuration for this customer.

        Args:
            customer_ip: Optional customer IP address (will read from .env if not provided)

        Returns:
            True if successful

        Raises:
            EnvironmentError: If environment file issues occur
            ValidationError: If IP validation fails
            APIError: If API communication fails
        """
        self.logger.info(f"Setting up web proxy for customer: {self.customer_name}")

        # Get customer IP if not provided
        if customer_ip:
            if not validate_ip_address(customer_ip):
                raise ValidationError(f"Invalid customer IP address: {customer_ip}")
            self.customer_ip = customer_ip
            self.logger.info(f"Using provided customer IP: {customer_ip}")
        else:
            self.customer_ip = self.get_customer_ip_from_env()

        # Prepare API payload
        payload = {
            "ip": self.customer_ip,
            "customer": self.customer_name,
            "timestamp": int(time.time())
        }

        with WebProxyAPISession(self.api_base) as session:
            # Check API health
            if not self.check_api_health(session):
                print_warning("Web proxy API health check failed, but continuing...")
            
            # Check if proxy already exists
            existing_status = self.get_proxy_status(session)
            if existing_status:
                existing_ip = existing_status.get('ip', '')
                if existing_ip == self.customer_ip:
                    print_success(f"Web proxy for {self.customer_name} already configured with correct IP {self.customer_ip}")
                    return True
                else:
                    print_warning(f"Web proxy exists with different IP {existing_ip}, updating to {self.customer_ip}")

            # Make API request
            endpoint = f"{WEBPROXY_ADD_ENDPOINT.lstrip('/')}/{self.customer_name}"
            response = session.request("POST", endpoint, json=payload)

            if response.success:
                self.logger.info(f"Web proxy setup completed for {self.customer_name} with IP {self.customer_ip}")
                return True
            else:
                error_msg = f"Web proxy setup failed: {response.message}"
                self.logger.error(error_msg)
                raise APIError(error_msg)

    def delete_webproxy(self) -> bool:
        """
        Delete web proxy configuration for this customer.

        Returns:
            True if successful

        Raises:
            APIError: If API communication fails
        """
        self.logger.info(f"Deleting web proxy for customer: {self.customer_name}")

        with WebProxyAPISession(self.api_base) as session:
            # Check if proxy exists
            existing_status = self.get_proxy_status(session)
            if not existing_status:
                print_success(f"Web proxy for {self.customer_name} does not exist, nothing to delete")
                return True

            # Make API request
            endpoint = f"{WEBPROXY_DELETE_ENDPOINT.lstrip('/')}/{self.customer_name}"
            response = session.request("DELETE", endpoint)

            if response.success:
                self.logger.info(f"Web proxy deletion completed for {self.customer_name}")
                return True
            elif response.status_code in API_NOT_FOUND_CODES:
                print_success(f"Web proxy for {self.customer_name} was already deleted")
                return True
            else:
                error_msg = f"Web proxy deletion failed: {response.message}"
                self.logger.error(error_msg)
                raise APIError(error_msg)

    def show_status(self) -> bool:
        """
        Show current web proxy status for this customer.

        Returns:
            True if successful
        """
        print_step(f"Checking web proxy status for customer: {self.customer_name}")

        try:
            with WebProxyAPISession(self.api_base) as session:
                # Check API connectivity
                if not self.check_api_health(session):
                    print_error("Cannot connect to web proxy API")
                    return False

                # Get proxy status
                status = self.get_proxy_status(session)

                print(f"\nWeb Proxy Status for customer: {self.customer_name}")
                print("=" * 50)

                if status:
                    print(f"✓ Proxy configured")
                    print(f"  - Customer: {status.get('customer', 'N/A')}")
                    print(f"  - IP Address: {status.get('ip', 'N/A')}")
                    print(f"  - Status: {status.get('status', 'N/A')}")
                    if 'created' in status:
                        print(f"  - Created: {status['created']}")
                    if 'last_updated' in status:
                        print(f"  - Last Updated: {status['last_updated']}")
                else:
                    print(f"✗ No proxy configuration found for {self.customer_name}")

                return True

        except Exception as e:
            print_error(f"Failed to check web proxy status: {e}")
            self.logger.error(f"Status check failed: {e}", exc_info=True)
            return False

    def validate_configuration(self) -> bool:
        """
        Validate the web proxy configuration.

        Returns:
            True if configuration is valid
        """
        print_step(f"Validating web proxy configuration for customer: {self.customer_name}")

        try:
            # Get customer IP from environment
            customer_ip = self.get_customer_ip_from_env()

            with WebProxyAPISession(self.api_base) as session:
                # Check API connectivity
                if not self.check_api_health(session):
                    print_error("Cannot connect to web proxy API")
                    return False

                # Get current status
                status = self.get_proxy_status(session)

                if not status:
                    print_error(f"No proxy configuration found for {self.customer_name}")
                    return False

                # Validate IP matches
                configured_ip = status.get('ip', '')
                if configured_ip != customer_ip:
                    print_error(f"IP mismatch: configured={configured_ip}, expected={customer_ip}")
                    return False

                print_success(f"Web proxy configuration for {self.customer_name} is valid")
                return True

        except Exception as e:
            print_error(f"Configuration validation failed: {e}")
            self.logger.error(f"Validation failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str, customer_ip: Optional[str] = None) -> bool:
        """
        Main execution function that orchestrates the web proxy setup process.

        Args:
            operation: Operation to perform ('setup', 'delete', 'status', 'validate')
            customer_ip: Optional customer IP address

        Returns:
            True if operation completed successfully
        """
        print_header(f"LEOS360 Web Proxy Configuration - {self.customer_name}")
        self.logger.info(f"Starting web proxy {operation} for {self.customer_name}")

        try:
            if operation == "delete":
                success = self.delete_webproxy()
            elif operation == "status":
                success = self.show_status()
            elif operation == "validate":
                success = self.validate_configuration()
            else:  # default: setup
                success = self.setup_webproxy(customer_ip)

            if success:
                print_header("OPERATION COMPLETED SUCCESSFULLY")
                print_success(f"Web proxy {operation} for {self.customer_name} completed successfully!")
                self.logger.info(f"Web proxy {operation} completed successfully")
            else:
                print_error(f"Web proxy {operation} failed for {self.customer_name}")
                self.logger.error(f"Web proxy {operation} failed")

            return success

        except (ValidationError, ConfigurationError, EnvironmentError, APIError) as e:
            print_error(f"Web proxy {operation} failed: {str(e)}")
            self.logger.error(f"Web proxy {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during web proxy {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during web proxy {operation}: {str(e)}", exc_info=True)
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute web proxy setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant web proxy for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage2_tenant_webproxy.py example-customer
  python3 stage2_tenant_webproxy.py example-customer --ip ***********
  python3 stage2_tenant_webproxy.py example-customer --delete
  python3 stage2_tenant_webproxy.py example-customer --status
  python3 stage2_tenant_webproxy.py example-customer --validate

Requirements:
  - Stage 1 must be completed successfully
  - Web proxy API must be accessible at ************:5000
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--ip',
        help='IP address of the customer (optional, will be read from .env if not provided)'
    )
    
    operation_group = parser.add_mutually_exclusive_group()
    operation_group.add_argument(
        '--delete',
        action='store_true',
        help='Delete the web proxy configuration for the customer'
    )
    operation_group.add_argument(
        '--status',
        action='store_true',
        help='Show current web proxy status for the customer'
    )
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate the web proxy configuration'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Validate IP argument if provided
    if args.ip and not validate_ip_address(args.ip):
        print_error(f"Invalid IP address: {args.ip}")
        sys.exit(1)

    # Determine operation
    if args.delete:
        operation = "delete"
    elif args.status:
        operation = "status"
    elif args.validate:
        operation = "validate"
    else:
        operation = "setup"

    try:
        # Create and run web proxy setup
        setup = TenantWebProxySetup(args.customer_name)
        success = setup.run(operation, args.ip)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError, EnvironmentError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Web proxy setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()