#!/usr/bin/env python3.12
"""
LEOS360 Platform - Common Functions Library
===========================================

This module contains common functions and utilities shared across all LEOS360 stage scripts.
It provides standardized logging, validation, error handling, and utility functions to
reduce code duplication and ensure consistency across the platform.

Author: LEOS360 Development Team
Version: 3.0
Last Updated: 2025-6-2

Features:
- Standardized logging setup with file and console handlers
- Common validation functions for customer names, IPs, URLs
- Unified print functions for consistent output formatting
- HTTP session management with retry logic
- File and directory operations with proper permissions
- Exception classes for different error types
"""

import os
import sys
import re
import time
import logging
import ipaddress
from pathlib import Path
from typing import Optional, Dict, Any, List, Union
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# Modern retry mechanism (fallback to requests retry if not available)
try:
    from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
    TENACITY_AVAILABLE = True
except ImportError:
    TENACITY_AVAILABLE = False

# Modern structured logging (fallback to standard logging if not available)
try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

# =============================================================================
# VERSION AND CONSTANTS
# =============================================================================

COMMON_VERSION = "3.0"
COMMON_NAME = "LEOS360 Common Functions Library"

# Base paths and configuration
BASE_DOMAIN = "leos360.cloud"
TENANTS_BASE_PATH = Path("/mnt/storage/tenants")
MASTER_BASE_PATH = Path("/mnt/storage/setup")

# File permissions
DIR_PERMISSIONS = 0o755
FILE_PERMISSIONS = 0o644
SCRIPT_PERMISSIONS = 0o755
SECRET_DIR_PERMISSIONS = 0o750
SECRET_FILE_PERMISSIONS = 0o640
LOG_PERMISSIONS = 0o644

# Network configuration
IP_NETWORKS = [
    ipaddress.IPv4Network('**********/24'),  # Customer network
    ipaddress.IPv4Network('10.0.0.0/8'),     # Internal networks
    ipaddress.IPv4Network('***********/16')   # Private networks
]

# Validation patterns
CUSTOMER_NAME_PATTERN = r'^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$'
DNS_NAME_PATTERN = r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.?$'

# API configuration defaults
DEFAULT_API_TIMEOUT = 30
DEFAULT_API_RETRIES = 3
DEFAULT_API_BACKOFF_FACTOR = 0.3
DEFAULT_CONNECT_TIMEOUT = 10
DEFAULT_READ_TIMEOUT = 20

# =============================================================================
# CUSTOM EXCEPTIONS
# =============================================================================

class LEOS360Error(Exception):
    """Base exception for LEOS360 platform errors."""
    pass

class ConfigurationError(LEOS360Error):
    """Raised when configuration is invalid."""
    pass

class ValidationError(LEOS360Error):
    """Raised when validation fails."""
    pass

class APIError(LEOS360Error):
    """Raised when API operations fail."""
    pass

class EnvironmentError(LEOS360Error):
    """Raised when environment file issues occur."""
    pass

class FileOperationError(LEOS360Error):
    """Raised when file operations fail."""
    pass

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

def setup_logging(customer_name: str, script_name: str = "leos360",
                 structured: bool = True) -> logging.Logger:
    """
    Setup modern logging configuration with structured logging support.

    Uses structlog if available for JSON-formatted, structured logs,
    otherwise falls back to standard logging with enhanced formatting.

    Args:
        customer_name: Name of the customer/tenant
        script_name: Name of the script for logger identification
        structured: Whether to use structured logging (if available)

    Returns:
        Configured logger instance
    """
    logger_name = f'{script_name}_{customer_name}'

    if STRUCTLOG_AVAILABLE and structured:
        # Configure structured logging with JSON output
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

        # Get structured logger
        logger = structlog.get_logger(logger_name)

        # Add context information
        logger = logger.bind(
            customer=customer_name,
            script=script_name,
            version=COMMON_VERSION
        )

        return logger
    else:
        # Fallback to standard logging with enhanced formatting
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # Avoid duplicate handlers
        if logger.handlers:
            return logger

        # Enhanced formatter with more context
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # File handler (if possible)
        try:
            log_dir = TENANTS_BASE_PATH / customer_name / "logs"
            log_dir.mkdir(parents=True, exist_ok=True)

            log_file = log_dir / f"{script_name}.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            # Set log file permissions
            os.chmod(log_file, LOG_PERMISSIONS)
        except (OSError, PermissionError) as e:
            print(f"[WARNING] Could not setup file logging: {e}")

        return logger

# =============================================================================
# PRINT FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")

def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")

def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")

def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

def validate_customer_name(customer_name: str) -> bool:
    """
    Validate customer name format using modern validation.

    Uses Pydantic models if available for enhanced validation,
    otherwise falls back to regex pattern matching.

    Args:
        customer_name: Customer name to validate

    Returns:
        True if valid, False otherwise
    """
    if TENACITY_AVAILABLE:  # Using same availability check for consistency
        try:
            from leos360_models import validate_customer_config
            # Use a dummy IP and domain for name-only validation
            validate_customer_config(customer_name, "************", "example.com")
            return True
        except (ImportError, ValueError):
            pass

    # Fallback to regex validation
    return bool(re.match(CUSTOMER_NAME_PATTERN, customer_name))

def validate_ip_address(ip_str: str) -> bool:
    """
    Validate IP address format and network membership.
    
    Args:
        ip_str: IP address string to validate
        
    Returns:
        True if valid and in allowed networks
    """
    try:
        ip = ipaddress.IPv4Address(ip_str)
        # Check if IP is in any of the allowed networks
        return any(ip in network for network in IP_NETWORKS)
    except (ipaddress.AddressValueError, ValueError):
        return False

def validate_dns_name(dns_name: str) -> bool:
    """
    Validate DNS name format.
    
    Args:
        dns_name: DNS name to validate
        
    Returns:
        True if valid, False otherwise
    """
    return bool(re.match(DNS_NAME_PATTERN, dns_name))

def validate_url(url: str) -> bool:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
    except Exception:
        return False

# =============================================================================
# FILE AND DIRECTORY OPERATIONS
# =============================================================================

def ensure_directory(path: Union[str, Path], permissions: int = DIR_PERMISSIONS) -> Path:
    """
    Ensure directory exists with proper permissions.
    
    Args:
        path: Directory path to create
        permissions: Directory permissions (default: 0o755)
        
    Returns:
        Path object of the created directory
        
    Raises:
        FileOperationError: If directory creation fails
    """
    path = Path(path)
    try:
        path.mkdir(parents=True, exist_ok=True)
        os.chmod(path, permissions)
        return path
    except (OSError, PermissionError) as e:
        raise FileOperationError(f"Failed to create directory {path}: {str(e)}")

def safe_write_file(file_path: Union[str, Path], content: str, 
                   permissions: int = FILE_PERMISSIONS, encoding: str = 'utf-8') -> None:
    """
    Safely write content to file with proper permissions.
    
    Args:
        file_path: Path to the file
        content: Content to write
        permissions: File permissions (default: 0o644)
        encoding: File encoding (default: utf-8)
        
    Raises:
        FileOperationError: If file writing fails
    """
    file_path = Path(file_path)
    try:
        # Ensure parent directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write content
        file_path.write_text(content, encoding=encoding)
        
        # Set permissions
        os.chmod(file_path, permissions)
    except (OSError, PermissionError, UnicodeError) as e:
        raise FileOperationError(f"Failed to write file {file_path}: {str(e)}")

def safe_read_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
    """
    Safely read content from file.

    Args:
        file_path: Path to the file
        encoding: File encoding (default: utf-8)

    Returns:
        File content as string

    Raises:
        FileOperationError: If file reading fails
    """
    file_path = Path(file_path)
    try:
        return file_path.read_text(encoding=encoding)
    except (OSError, UnicodeError) as e:
        raise FileOperationError(f"Failed to read file {file_path}: {str(e)}")

# =============================================================================
# HTTP SESSION MANAGEMENT
# =============================================================================

class APISession:
    """
    Generic HTTP session manager with retry logic and proper error handling.
    Can be subclassed for specific API implementations.
    """

    def __init__(self, base_url: str, timeout: int = DEFAULT_API_TIMEOUT,
                 retries: int = DEFAULT_API_RETRIES,
                 backoff_factor: float = DEFAULT_API_BACKOFF_FACTOR,
                 headers: Optional[Dict[str, str]] = None):
        """
        Initialize API session.

        Args:
            base_url: Base URL for the API
            timeout: Request timeout in seconds
            retries: Number of retry attempts
            backoff_factor: Backoff factor for retries
            headers: Additional headers to include
        """
        if not validate_url(base_url):
            raise ConfigurationError(f"Invalid base URL: {base_url}")

        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = self._create_session(retries, backoff_factor, headers)

    def _create_session(self, retries: int, backoff_factor: float,
                       headers: Optional[Dict[str, str]]) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=retries,
            backoff_factor=backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "PATCH", "DELETE"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set default headers
        default_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": f"LEOS360-Platform/{COMMON_VERSION}"
        }

        if headers:
            default_headers.update(headers)

        session.headers.update(default_headers)

        return session

    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an API request with proper error handling.

        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            **kwargs: Additional request arguments

        Returns:
            Response object

        Raises:
            APIError: If request fails
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=(DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT),
                **kwargs
            )
            response.raise_for_status()
            return response

        except requests.exceptions.Timeout:
            raise APIError(f"Request timed out after {self.timeout} seconds")
        except requests.exceptions.ConnectionError:
            raise APIError(f"Connection failed to {url}")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise APIError("Authentication failed - check credentials")
            elif e.response.status_code == 403:
                raise APIError("Access forbidden - insufficient permissions")
            elif e.response.status_code == 404:
                raise APIError(f"Resource not found: {url}")
            elif e.response.status_code == 422:
                raise APIError(f"Invalid data: {e.response.text}")
            else:
                raise APIError(f"HTTP {e.response.status_code}: {e.response.text}")
        except requests.exceptions.RequestException as e:
            raise APIError(f"Request failed: {str(e)}")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()

# =============================================================================
# ENVIRONMENT HANDLING
# =============================================================================

def load_env_file(env_file_path: Union[str, Path]) -> Dict[str, str]:
    """
    Load environment variables from .env file.

    Args:
        env_file_path: Path to the .env file

    Returns:
        Dictionary of environment variables

    Raises:
        EnvironmentError: If file cannot be read or parsed
    """
    env_file_path = Path(env_file_path)

    if not env_file_path.exists():
        raise EnvironmentError(f"Environment file does not exist: {env_file_path}")

    try:
        from dotenv import dotenv_values
        env_vars = dotenv_values(env_file_path)
        return {k: v for k, v in env_vars.items() if v is not None}
    except Exception as e:
        raise EnvironmentError(f"Failed to load environment file {env_file_path}: {str(e)}")

def get_env_variable(env_vars: Dict[str, str], var_name: str,
                    required: bool = True, default: Optional[str] = None) -> Optional[str]:
    """
    Get environment variable with validation.

    Args:
        env_vars: Dictionary of environment variables
        var_name: Name of the variable to get
        required: Whether the variable is required
        default: Default value if variable is not found

    Returns:
        Variable value or default

    Raises:
        EnvironmentError: If required variable is missing
    """
    value = env_vars.get(var_name, default)

    if required and not value:
        raise EnvironmentError(f"Required environment variable {var_name} is missing or empty")

    return value.strip() if value else value

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def replace_variables(content: str, replacements: Dict[str, str]) -> str:
    """
    Replace variables in content using dictionary.

    Args:
        content: Content with variables to replace
        replacements: Dictionary of variable replacements

    Returns:
        Content with variables replaced
    """
    for key, value in replacements.items():
        # Support both ${VAR} and {{VAR}} formats
        content = content.replace(f"${{{key}}}", str(value))
        content = content.replace(f"{{{{{key}}}}}", str(value))

    return content

def generate_secure_password(length: int = 32) -> str:
    """
    Generate a secure random password.

    Args:
        length: Length of the password

    Returns:
        Secure random password
    """
    import secrets
    import string

    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def wait_with_progress(seconds: int, message: str = "Waiting") -> None:
    """
    Wait with progress indication.

    Args:
        seconds: Number of seconds to wait
        message: Message to display
    """
    print(f"[INFO] {message} for {seconds} seconds...")
    for i in range(seconds):
        print(f"\r[INFO] {message}... {seconds - i} seconds remaining", end="", flush=True)
        time.sleep(1)
    print(f"\r[INFO] {message}... completed!" + " " * 20)
