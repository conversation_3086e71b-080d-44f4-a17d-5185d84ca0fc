#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 3: Docker Stack Deployment
===================================================

This script handles the Docker stack deployment for a new tenant in the LEOS360 platform.
It deploys or updates Docker Compose stacks using the Portainer API, managing the complete
containerized infrastructure for the customer.

Author: LEOS360 Development Team
Version: 3.0
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 and Stage 2 must be completed successfully
- Portainer API must be accessible
- Docker Compose file must exist in tenant directory
- Portainer API credentials must be configured

Usage:
    python3 stage3_tenant_docker.py <customer_name> [options]

Examples:
    python3 stage3_tenant_docker.py example-customer
    python3 stage3_tenant_docker.py example-customer --delete
    python3 stage3_tenant_docker.py example-customer --status
    python3 stage3_tenant_docker.py example-customer --skip-keycloak-check
    python3 stage3_tenant_docker.py example-customer --validate
"""

import sys
import json
import time
import argparse
import re
import hashlib
import yaml
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, field
import requests
import urllib3

# Import common functions from LEOS360 platform
from leos360_common import (
    # Exception classes
    LEOS360Error, ConfigurationError, ValidationError, APIError,
    # Logging functions
    setup_logging,
    # Print functions
    print_header, print_step, print_warning, print_error, print_success,
    # Validation functions
    validate_customer_name,
    # Environment handling
    load_env_file, get_env_variable,
    # HTTP session management
    APISession,
    # Constants
    TENANTS_BASE_PATH
)

# =============================================================================
# DOCKER-SPECIFIC EXCEPTIONS
# =============================================================================

class DockerStackError(LEOS360Error):
    """Base exception for Docker stack errors."""
    pass

class StackNotFoundError(DockerStackError):
    """Raised when stack is not found."""
    pass

class ContainerError(DockerStackError):
    """Raised when container operations fail."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "3.0"
SCRIPT_NAME = "Stage 3: Docker Stack Deployment"

# File paths
DOCKER_CONFIG_PATH = Path("/mnt/storage/docker/.env")
BASE_TENANT_PATH = TENANTS_BASE_PATH  # Use common constant

# Portainer configuration
DEFAULT_ENDPOINT_ID = "11"
PORTAINER_PORT = 9443
API_TIMEOUT = 60
API_RETRIES = 3
API_BACKOFF_FACTOR = 0.3
CONNECT_TIMEOUT = 15
READ_TIMEOUT = 45

# Stack deployment configuration
STACK_DEPLOYMENT_TIMEOUT = 600  # 10 minutes
STACK_DELETION_TIMEOUT = 300    # 5 minutes
STACK_UPDATE_TIMEOUT = 900      # 15 minutes

# Container monitoring configuration
KEYCLOAK_STARTUP_TIMEOUT = 600  # 10 minutes timeout for Keycloak startup
KEYCLOAK_LOG_CHECK_INTERVAL = 10  # Check logs every 10 seconds
KEYCLOAK_MAX_LOG_LINES = 100    # Maximum log lines to fetch
KEYCLOAK_STARTUP_PATTERNS = [
    "Keycloak.*started in",
    "Profile prod activated",
    "Listening on.*8080",
    "Deployment.*deployed"
]

# Docker-specific validation patterns
STACK_NAME_PATTERN = r'^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$'

# Disable SSL warnings but use proper session configuration
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# =============================================================================
# DATA CLASSES
# =============================================================================

@dataclass
class StackInfo:
    """Information about a Docker stack."""
    id: str
    name: str
    status: str
    endpoint_id: str
    creation_date: Optional[str] = None
    update_date: Optional[str] = None
    env_vars: List[Dict[str, str]] = field(default_factory=list)

@dataclass
class ContainerInfo:
    """Information about a Docker container."""
    id: str
    name: str
    status: str
    state: str
    image: str
    created: Optional[str] = None

@dataclass
class DeploymentResult:
    """Result of a deployment operation."""
    success: bool
    operation: str  # 'create', 'update', 'delete'
    stack_id: Optional[str] = None
    message: str = ""
    errors: List[str] = field(default_factory=list)


# =============================================================================
# DOCKER-SPECIFIC UTILITY FUNCTIONS
# =============================================================================

def print_debug(message: str) -> None:
    """Print a formatted debug message."""
    print(f"[DEBUG] {message}")


def validate_stack_name(stack_name: str) -> bool:
    """
    Validate stack name format.
    
    Args:
        stack_name: Stack name to validate
        
    Returns:
        True if valid, False otherwise
    """
    return bool(re.match(STACK_NAME_PATTERN, stack_name))


def validate_compose_file_syntax(compose_file: Path) -> bool:
    """
    Validate Docker Compose file syntax.
    
    Args:
        compose_file: Path to compose file
        
    Returns:
        True if valid syntax, False otherwise
    """
    try:
        with open(compose_file, 'r', encoding='utf-8') as f:
            yaml.safe_load(f)
        return True
    except (yaml.YAMLError, IOError, UnicodeDecodeError):
        return False


def calculate_file_hash(file_path: Path) -> str:
    """
    Calculate SHA256 hash of a file.
    
    Args:
        file_path: Path to file
        
    Returns:
        SHA256 hash as hex string
    """
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    return sha256_hash.hexdigest()


# =============================================================================
# PORTAINER API SESSION
# =============================================================================

class PortainerAPISession(APISession):
    """Secure HTTP session manager for Portainer API with retry logic."""

    def __init__(self, api_key: str, host: str, port: int = PORTAINER_PORT, verify_ssl: bool = False):
        self.api_key = api_key
        self.host = host
        self.port = port
        self.verify_ssl = verify_ssl

        # Build base URL for Portainer API
        base_url = f"https://{host}:{port}/api"

        # Custom headers for Portainer API
        headers = {
            "X-API-Key": api_key,
            "Accept": "application/json",
            "User-Agent": f"LEOS360-Docker-Setup/{SCRIPT_VERSION}"
        }

        # Initialize parent APISession
        super().__init__(
            base_url=base_url,
            timeout=API_TIMEOUT,
            retries=API_RETRIES,
            backoff_factor=API_BACKOFF_FACTOR,
            headers=headers
        )
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an API request with Portainer-specific error handling.

        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            **kwargs: Additional request arguments

        Returns:
            Response object

        Raises:
            APIError: If request fails
        """
        # Set SSL verification for Portainer
        if 'verify' not in kwargs:
            kwargs['verify'] = self.verify_ssl

        # Use parent's request method with Portainer-specific SSL handling
        return super().request(method, endpoint, **kwargs)


# =============================================================================
# MAIN DOCKER STACK DEPLOYMENT CLASS
# =============================================================================

class TenantDockerStackSetup:
    """
    Handles Docker stack deployment for a new tenant in the LEOS360 platform.

    This class manages the complete Docker stack deployment process including:
    - Portainer API configuration and authentication
    - Docker Compose file validation and parsing
    - Environment variable management
    - Stack creation, updating, and deletion
    - Container monitoring and health checks
    - Error handling and status reporting
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Docker stack setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If required files are missing
        """
        # Validate and normalize customer name
        customer_name = customer_name.strip().lower()
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name

        # Setup logging
        self.logger = setup_logging(customer_name, "docker_setup")
        self.logger.info(f"Initializing Docker stack setup for: {customer_name}")

        # Stack configuration
        self.stack_name = f"leos360-{customer_name}"
        
        # Validate stack name
        if not validate_stack_name(self.stack_name):
            raise ValidationError(f"Invalid stack name: {self.stack_name}")

        # File paths
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"

        # Validate customer directory exists
        if not self.customer_dir.exists():
            raise ConfigurationError(f"Customer directory does not exist: {self.customer_dir}")

        # Find and validate docker-compose file
        self.compose_file = self._find_and_validate_compose_file()

        # Portainer configuration (will be loaded later)
        self.portainer_config = {}
        self.api_session: Optional[PortainerAPISession] = None

        # Configuration options
        self.skip_keycloak_check = False

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def _find_and_validate_compose_file(self) -> Path:
        """
        Find and validate the Docker Compose file in the customer directory.

        Returns:
            Path to the Docker Compose file

        Raises:
            ConfigurationError: If no valid compose file is found
        """
        # Check for docker-compose files in order of preference
        compose_files = [
            self.customer_dir / "docker-compose.yaml",
            self.customer_dir / "docker-compose.yml",
            self.customer_dir / "compose.yaml",
            self.customer_dir / "compose.yml"
        ]

        for compose_file in compose_files:
            if compose_file.exists():
                # Validate syntax
                if validate_compose_file_syntax(compose_file):
                    self.logger.info(f"Found valid compose file: {compose_file}")
                    return compose_file
                else:
                    self.logger.warning(f"Invalid syntax in compose file: {compose_file}")

        raise ConfigurationError(f"No valid docker-compose file found in {self.customer_dir}")

    def load_portainer_config(self) -> None:
        """
        Load Portainer configuration from environment file.

        Raises:
            ConfigurationError: If config file doesn't exist or is invalid
        """
        try:
            # Use common environment loading function
            env_vars = load_env_file(DOCKER_CONFIG_PATH)

            # Get required variables using common function
            api_key = get_env_variable(env_vars, "PORTAINER_API_KEY", required=True)
            host = get_env_variable(env_vars, "PORTAINER_HOST", required=True)

            # Validate API key format (basic check)
            if len(api_key) < 20:
                raise ConfigurationError("PORTAINER_API_KEY appears to be too short")

            # Validate host format
            if not self._validate_hostname(host):
                raise ConfigurationError(f"Invalid PORTAINER_HOST format: {host}")

            # Get optional variables with defaults
            endpoint_id = get_env_variable(env_vars, "ENDPOINT_ID", required=False, default=DEFAULT_ENDPOINT_ID)
            port_str = get_env_variable(env_vars, "PORTAINER_PORT", required=False, default=str(PORTAINER_PORT))
            verify_ssl_str = get_env_variable(env_vars, "PORTAINER_VERIFY_SSL", required=False, default="false")

            self.portainer_config = {
                "api_key": api_key,
                "host": host,
                "endpoint_id": endpoint_id,
                "port": int(port_str),
                "verify_ssl": verify_ssl_str.lower() == "true"
            }

            self.logger.info("Portainer configuration loaded successfully")

        except (ValueError, TypeError) as e:
            raise ConfigurationError(f"Invalid Portainer configuration: {e}")

    def _validate_hostname(self, hostname: str) -> bool:
        """Validate hostname format."""
        try:
            # Check if it's an IP address
            import ipaddress
            ipaddress.ip_address(hostname)
            return True
        except ValueError:
            # Check if it's a valid hostname
            return re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$', hostname) is not None

    def parse_env_file(self) -> List[Dict[str, str]]:
        """
        Parse environment variables from customer .env file with validation.

        Returns:
            List of environment variable dictionaries

        Raises:
            ConfigurationError: If env file is invalid
        """
        env_vars = []

        if not self.env_file.exists():
            self.logger.warning("No .env file found for customer")
            return env_vars

        try:
            # Use common environment loading function
            env_dict = load_env_file(self.env_file)

            for key, value in env_dict.items():
                if key and value is not None:  # Skip empty keys and None values
                    env_vars.append({"name": key.strip(), "value": str(value).strip()})

            self.logger.info(f"Parsed {len(env_vars)} environment variables")
            return env_vars

        except Exception as e:
            raise ConfigurationError(f"Error parsing environment file: {e}")

    def _create_api_session(self) -> PortainerAPISession:
        """Create and return API session."""
        if not self.portainer_config:
            raise ConfigurationError("Portainer configuration not loaded")
            
        return PortainerAPISession(
            api_key=self.portainer_config["api_key"],
            host=self.portainer_config["host"],
            port=self.portainer_config["port"],
            verify_ssl=self.portainer_config["verify_ssl"]
        )

    # =========================================================================
    # PORTAINER API METHODS
    # =========================================================================

    def test_api_connectivity(self) -> bool:
        """
        Test Portainer API connectivity and authentication.

        Returns:
            True if connection successful
        """
        try:
            with self._create_api_session() as session:
                response = session.request("GET", "status")
                if response.status_code == 200:
                    self.logger.info("Portainer API connectivity test successful")
                    return True
                else:
                    self.logger.error(f"API connectivity test failed: {response.status_code}")
                    return False
        except Exception as e:
            self.logger.error(f"API connectivity test failed: {e}")
            return False

    def get_existing_stacks(self) -> List[StackInfo]:
        """
        Get list of existing stacks from Portainer.

        Returns:
            List of StackInfo objects

        Raises:
            APIError: If API request fails
        """
        with self._create_api_session() as session:
            response = session.request("GET", "stacks")
            
            if response.status_code != 200:
                raise APIError(f"Failed to get stacks: {response.status_code} - {response.text}")

            stacks_data = response.json()
            stacks = []
            
            for stack_data in stacks_data:
                stack = StackInfo(
                    id=str(stack_data.get("Id", "")),
                    name=stack_data.get("Name", ""),
                    status=stack_data.get("Status", ""),
                    endpoint_id=str(stack_data.get("EndpointId", "")),
                    creation_date=stack_data.get("CreationDate"),
                    update_date=stack_data.get("UpdateDate"),
                    env_vars=stack_data.get("Env", [])
                )
                stacks.append(stack)
                
            return stacks

    def find_stack_by_name(self, stack_name: str) -> Optional[StackInfo]:
        """
        Find a stack by name.

        Args:
            stack_name: Name of the stack to find

        Returns:
            StackInfo if found, None otherwise
        """
        stacks = self.get_existing_stacks()
        
        for stack in stacks:
            if stack.name == stack_name:
                return stack
                
        return None

    # =========================================================================
    # CONTAINER MONITORING METHODS
    # =========================================================================

    def get_containers_for_stack(self, stack_name: str) -> List[ContainerInfo]:
        """
        Get containers for a specific stack.

        Args:
            stack_name: Name of the stack

        Returns:
            List of ContainerInfo objects
        """
        containers = []
        
        try:
            with self._create_api_session() as session:
                endpoint_id = self.portainer_config["endpoint_id"]
                response = session.request("GET", f"endpoints/{endpoint_id}/docker/containers/json?all=true")

                if response.status_code != 200:
                    self.logger.error(f"Failed to get containers: {response.status_code}")
                    return containers

                containers_data = response.json()

                for container_data in containers_data:
                    labels = container_data.get("Labels", {})
                    container_stack = labels.get("com.docker.compose.project", "")
                    
                    if container_stack == stack_name:
                        names = container_data.get("Names", [])
                        name = names[0].lstrip("/") if names else "unknown"
                        
                        container = ContainerInfo(
                            id=container_data.get("Id", ""),
                            name=name,
                            status=container_data.get("Status", ""),
                            state=container_data.get("State", ""),
                            image=container_data.get("Image", ""),
                            created=container_data.get("Created")
                        )
                        containers.append(container)

        except Exception as e:
            self.logger.error(f"Exception while getting containers: {e}")

        return containers

    def find_container_by_service_name(self, service_name: str) -> Optional[ContainerInfo]:
        """
        Find a container by service name within the stack.

        Args:
            service_name: Name of the service (e.g., 'keycloak')

        Returns:
            ContainerInfo if found, None otherwise
        """
        containers = self.get_containers_for_stack(self.stack_name)
        
        for container in containers:
            # Check if container name ends with the service name
            if container.name.endswith(f"-{service_name}"):
                return container
                
        return None

    def get_container_logs(self, container_id: str, tail_lines: int = KEYCLOAK_MAX_LOG_LINES) -> Optional[str]:
        """
        Get logs from a container.

        Args:
            container_id: ID of the container
            tail_lines: Number of recent lines to retrieve

        Returns:
            Container logs as string, None if failed
        """
        try:
            with self._create_api_session() as session:
                endpoint_id = self.portainer_config["endpoint_id"]
                params = {
                    "stdout": "true",
                    "stderr": "true",
                    "tail": str(tail_lines),
                    "timestamps": "true"
                }
                
                query_string = "&".join([f"{k}={v}" for k, v in params.items()])
                endpoint = f"endpoints/{endpoint_id}/docker/containers/{container_id}/logs?{query_string}"
                
                response = session.request("GET", endpoint)

                if response.status_code != 200:
                    self.logger.error(f"Failed to get container logs: {response.status_code}")
                    return None

                return response.text

        except Exception as e:
            self.logger.error(f"Exception while getting container logs: {e}")
            return None

    def wait_for_keycloak_ready(self) -> bool:
        """
        Wait for Keycloak container to be fully started and ready.

        Returns:
            True if Keycloak is ready, False if timeout or error
        """
        print_step(f"Waiting for Keycloak container to be ready...")
        self.logger.info("Starting Keycloak readiness check")

        start_time = time.time()
        last_log_check = 0

        while time.time() - start_time < KEYCLOAK_STARTUP_TIMEOUT:
            try:
                # Find Keycloak container
                container = self.find_container_by_service_name("keycloak")

                if not container:
                    self.logger.debug("Keycloak container not found yet")
                    time.sleep(KEYCLOAK_LOG_CHECK_INTERVAL)
                    continue

                # Check container state
                if container.state not in ["running"]:
                    self.logger.debug(f"Keycloak container not running yet: {container.state}")
                    time.sleep(KEYCLOAK_LOG_CHECK_INTERVAL)
                    continue

                # Check logs periodically
                current_time = time.time()
                if current_time - last_log_check >= KEYCLOAK_LOG_CHECK_INTERVAL:
                    logs = self.get_container_logs(container.id, tail_lines=50)
                    
                    if logs and self._check_keycloak_startup_message(logs):
                        elapsed_time = current_time - start_time
                        print_success(f"Keycloak container is ready! (took {elapsed_time:.1f} seconds)")
                        self.logger.info(f"Keycloak ready after {elapsed_time:.1f} seconds")
                        return True
                    
                    last_log_check = current_time

                # Show progress
                remaining_time = KEYCLOAK_STARTUP_TIMEOUT - (time.time() - start_time)
                if remaining_time > 0:
                    print_debug(f"Keycloak not ready yet, timeout in {remaining_time:.1f}s")

                time.sleep(2)  # Short sleep to avoid busy waiting

            except Exception as e:
                self.logger.warning(f"Error checking Keycloak status: {e}")
                time.sleep(KEYCLOAK_LOG_CHECK_INTERVAL)

        # Timeout reached
        elapsed_time = time.time() - start_time
        print_error(f"Timeout waiting for Keycloak to be ready after {elapsed_time:.1f} seconds")
        self.logger.error(f"Keycloak readiness timeout after {elapsed_time:.1f} seconds")
        return False

    def _check_keycloak_startup_message(self, logs: str) -> bool:
        """
        Check if Keycloak startup message is present in logs using patterns.

        Args:
            logs: Container logs to check

        Returns:
            True if startup message found, False otherwise
        """
        # Convert logs to lowercase for case-insensitive matching
        logs_lower = logs.lower()
        
        found_patterns = []
        for pattern in KEYCLOAK_STARTUP_PATTERNS:
            if re.search(pattern.lower(), logs_lower):
                found_patterns.append(pattern)

        # We need at least 2 patterns to confirm startup
        if len(found_patterns) >= 2:
            self.logger.debug(f"Found Keycloak startup patterns: {found_patterns}")
            return True

        self.logger.debug(f"Keycloak startup patterns not yet complete. Found: {found_patterns}")
        return False

    # =========================================================================
    # STACK OPERATION METHODS
    # =========================================================================

    def create_stack(self, env_vars: List[Dict[str, str]]) -> DeploymentResult:
        """
        Create a new Docker stack.

        Args:
            env_vars: List of environment variables

        Returns:
            DeploymentResult
        """
        print_step(f"Creating new stack: {self.stack_name}")
        self.logger.info(f"Creating stack {self.stack_name}")

        try:
            with self._create_api_session() as session:
                # Prepare multipart form data
                with open(self.compose_file, 'rb') as f:
                    files = {'file': (self.compose_file.name, f, 'application/x-yaml')}
                    data = {
                        'Name': self.stack_name,
                        'Env': json.dumps(env_vars)
                    }

                    endpoint = f"stacks/create/standalone/file?endpointId={self.portainer_config['endpoint_id']}"
                    
                    # Remove Content-Type header for multipart upload
                    headers = {"X-API-Key": session.api_key}
                    
                    response = session.session.post(
                        f"{session.base_url}/{endpoint}",
                        files=files,
                        data=data,
                        headers=headers,
                        verify=session.verify_ssl,
                        timeout=(CONNECT_TIMEOUT, READ_TIMEOUT)
                    )

                    if 200 <= response.status_code < 300:
                        stack_data = response.json() if response.content else {}
                        stack_id = str(stack_data.get("Id", ""))
                        
                        print_success(f"Stack {self.stack_name} created successfully!")
                        self.logger.info(f"Stack created with ID: {stack_id}")
                        
                        return DeploymentResult(
                            success=True,
                            operation="create",
                            stack_id=stack_id,
                            message="Stack created successfully"
                        )
                    else:
                        error_msg = f"Failed to create stack: {response.status_code} - {response.text}"
                        self.logger.error(error_msg)
                        return DeploymentResult(
                            success=False,
                            operation="create",
                            message=error_msg,
                            errors=[response.text]
                        )

        except Exception as e:
            error_msg = f"Exception during stack creation: {e}"
            self.logger.error(error_msg)
            return DeploymentResult(
                success=False,
                operation="create",
                message=error_msg,
                errors=[str(e)]
            )

    def update_stack(self, stack_id: str, env_vars: List[Dict[str, str]]) -> DeploymentResult:
        """
        Update an existing Docker stack.

        Args:
            stack_id: ID of the stack to update
            env_vars: List of environment variables

        Returns:
            DeploymentResult
        """
        print_step(f"Updating existing stack: {self.stack_name} (ID: {stack_id})")
        self.logger.info(f"Updating stack {stack_id}")

        try:
            # Read compose file content
            with open(self.compose_file, 'r', encoding='utf-8') as f:
                compose_content = f.read()

            # Prepare update payload
            update_payload = {
                "stackFileContent": compose_content,
                "env": env_vars,
                "prune": True  # Remove unused containers
            }

            with self._create_api_session() as session:
                endpoint = f"stacks/{stack_id}?endpointId={self.portainer_config['endpoint_id']}"
                response = session.request("PUT", endpoint, json=update_payload)

                if 200 <= response.status_code < 300:
                    print_success(f"Stack {self.stack_name} updated successfully!")
                    self.logger.info("Stack updated successfully")
                    
                    return DeploymentResult(
                        success=True,
                        operation="update",
                        stack_id=stack_id,
                        message="Stack updated successfully"
                    )
                else:
                    error_msg = f"Failed to update stack: {response.status_code} - {response.text}"
                    self.logger.error(error_msg)
                    return DeploymentResult(
                        success=False,
                        operation="update",
                        stack_id=stack_id,
                        message=error_msg,
                        errors=[response.text]
                    )

        except Exception as e:
            error_msg = f"Exception during stack update: {e}"
            self.logger.error(error_msg)
            return DeploymentResult(
                success=False,
                operation="update",
                stack_id=stack_id,
                message=error_msg,
                errors=[str(e)]
            )

    def delete_stack(self, stack_id: str) -> DeploymentResult:
        """
        Delete a Docker stack.

        Args:
            stack_id: ID of the stack to delete

        Returns:
            DeploymentResult
        """
        print_step(f"Deleting stack: {self.stack_name} (ID: {stack_id})")
        self.logger.info(f"Deleting stack {stack_id}")

        try:
            with self._create_api_session() as session:
                response = session.request("DELETE", f"stacks/{stack_id}")

                if response.status_code in [200, 204, 404]:
                    print_success(f"Stack {self.stack_name} deleted successfully!")
                    self.logger.info("Stack deleted successfully")
                    
                    return DeploymentResult(
                        success=True,
                        operation="delete",
                        stack_id=stack_id,
                        message="Stack deleted successfully"
                    )
                else:
                    error_msg = f"Failed to delete stack: {response.status_code} - {response.text}"
                    self.logger.error(error_msg)
                    return DeploymentResult(
                        success=False,
                        operation="delete",
                        stack_id=stack_id,
                        message=error_msg,
                        errors=[response.text]
                    )

        except Exception as e:
            error_msg = f"Exception during stack deletion: {e}"
            self.logger.error(error_msg)
            return DeploymentResult(
                success=False,
                operation="delete",
                stack_id=stack_id,
                message=error_msg,
                errors=[str(e)]
            )

    # =========================================================================
    # STATUS AND VALIDATION METHODS
    # =========================================================================

    def show_stack_status(self) -> bool:
        """
        Show detailed status of the Docker stack for this customer.

        Returns:
            True if successful
        """
        print_step(f"Checking stack status for customer: {self.customer_name}")

        try:
            # Test API connectivity first
            if not self.test_api_connectivity():
                print_error("Cannot connect to Portainer API")
                return False

            existing_stack = self.find_stack_by_name(self.stack_name)

            print(f"\nDocker Stack Status for customer: {self.customer_name}")
            print("=" * 60)

            if existing_stack:
                print(f"✓ Stack: {existing_stack.name}")
                print(f"  - ID: {existing_stack.id}")
                print(f"  - Status: {existing_stack.status}")
                print(f"  - Endpoint ID: {existing_stack.endpoint_id}")
                print(f"  - Created: {existing_stack.creation_date or 'Unknown'}")
                print(f"  - Updated: {existing_stack.update_date or 'Never'}")
                
                # Show containers
                containers = self.get_containers_for_stack(self.stack_name)
                if containers:
                    print(f"\nContainers ({len(containers)}):")
                    for container in containers:
                        status_icon = "✓" if container.state == "running" else "✗"
                        print(f"  {status_icon} {container.name}")
                        print(f"    - State: {container.state}")
                        print(f"    - Status: {container.status}")
                        print(f"    - Image: {container.image}")
                else:
                    print("\n⚠ No containers found for this stack")
            else:
                print(f"✗ Stack {self.stack_name} does not exist")

            return True

        except Exception as e:
            print_error(f"Failed to check stack status: {e}")
            self.logger.error(f"Status check failed: {e}", exc_info=True)
            return False

    def validate_configuration(self) -> bool:
        """
        Validate the Docker stack configuration.

        Returns:
            True if configuration is valid
        """
        print_step(f"Validating Docker stack configuration for customer: {self.customer_name}")

        validation_errors = []

        try:
            # Validate compose file syntax
            if not validate_compose_file_syntax(self.compose_file):
                validation_errors.append(f"Invalid syntax in compose file: {self.compose_file}")

            # Validate environment file
            try:
                env_vars = self.parse_env_file()
                if not env_vars:
                    validation_errors.append("No environment variables found in .env file")
            except Exception as e:
                validation_errors.append(f"Invalid .env file: {e}")

            # Validate Portainer connectivity
            if not self.test_api_connectivity():
                validation_errors.append("Cannot connect to Portainer API")

            # Check for required environment variables
            required_vars = [
                "CUSTOMER_NAME", "CUSTOMER_DOMAIN", "CUSTOMER_IP",
                "CONFIG_BASE", "EXTERNAL_DB_HOST", "EXTERNAL_DB_USER"
            ]
            
            if self.env_file.exists():
                env_dict = load_env_file(self.env_file)
                missing_vars = [var for var in required_vars if var not in env_dict]
                if missing_vars:
                    validation_errors.append(f"Missing required environment variables: {missing_vars}")

            if validation_errors:
                print_error("Configuration validation failed:")
                for error in validation_errors:
                    print_error(f"  - {error}")
                return False
            else:
                print_success(f"Docker stack configuration for {self.customer_name} is valid")
                return True

        except Exception as e:
            print_error(f"Configuration validation failed: {e}")
            self.logger.error(f"Validation failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str) -> bool:
        """
        Main execution function that orchestrates the Docker stack deployment process.

        Args:
            operation: Operation to perform ('deploy', 'delete', 'status', 'validate')

        Returns:
            True if operation completed successfully
        """
        print_header(f"LEOS360 Docker Stack Deployment - {self.customer_name}")
        self.logger.info(f"Starting Docker stack {operation} for {self.customer_name}")

        try:
            # Step 1: Load Portainer configuration
            self.load_portainer_config()

            # Step 2: Test API connectivity
            if not self.test_api_connectivity():
                raise APIError("Cannot connect to Portainer API")

            # Step 3: Handle different operations
            if operation == "status":
                return self.show_stack_status()
            
            elif operation == "validate":
                return self.validate_configuration()
            
            elif operation == "delete":
                existing_stack = self.find_stack_by_name(self.stack_name)
                if existing_stack:
                    result = self.delete_stack(existing_stack.id)
                    if result.success:
                        print_success(f"Stack {self.stack_name} deleted successfully!")
                    return result.success
                else:
                    print_warning(f"Stack {self.stack_name} does not exist, nothing to delete")
                    return True
            
            else:  # default: deploy
                # Parse environment variables
                env_vars = self.parse_env_file()
                
                existing_stack = self.find_stack_by_name(self.stack_name)
                result = None

                if existing_stack:
                    # Update existing stack
                    result = self.update_stack(existing_stack.id, env_vars)
                    if not result.success:
                        # If update fails, try delete and recreate
                        print_warning("Update failed, attempting delete and recreate...")
                        delete_result = self.delete_stack(existing_stack.id)
                        if delete_result.success:
                            time.sleep(5)  # Wait for deletion to complete
                            result = self.create_stack(env_vars)
                else:
                    # Create new stack
                    result = self.create_stack(env_vars)

                if result and result.success:
                    print_header("DOCKER STACK DEPLOYED SUCCESSFULLY")
                    print_success(f"Docker stack deployment for {self.customer_name} completed successfully!")

                    # Handle Keycloak verification
                    if self.skip_keycloak_check:
                        print_warning("Skipping Keycloak startup verification (--skip-keycloak-check flag used)")
                        print_header("OPERATION COMPLETED SUCCESSFULLY")
                        print_success("Docker stack deployment completed successfully!")
                    else:
                        # Wait for Keycloak to be ready
                        print_step("Waiting for Keycloak container to be fully ready...")
                        keycloak_ready = self.wait_for_keycloak_ready()

                        if keycloak_ready:
                            print_header("OPERATION COMPLETED SUCCESSFULLY")
                            print_success("Docker stack deployment and Keycloak startup verification completed successfully!")
                            print_success("Ready to proceed with Stage 4 (Service Configuration)")
                        else:
                            print_warning("Docker stack deployed but Keycloak startup verification failed")
                            print_warning("You may need to check Keycloak container logs manually before proceeding to Stage 4")

                    return True
                else:
                    print_error("Docker stack deployment failed")
                    if result and result.errors:
                        for error in result.errors:
                            print_error(f"  - {error}")
                    return False

        except (ConfigurationError, ValidationError, APIError) as e:
            print_error(f"Docker stack {operation} failed: {str(e)}")
            self.logger.error(f"Docker stack {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during Docker stack {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during Docker stack {operation}: {str(e)}", exc_info=True)
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute Docker stack deployment.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Deploy Docker stack for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage3_tenant_docker.py example-customer
  python3 stage3_tenant_docker.py example-customer --delete
  python3 stage3_tenant_docker.py example-customer --status
  python3 stage3_tenant_docker.py example-customer --validate
  python3 stage3_tenant_docker.py example-customer --skip-keycloak-check

Requirements:
  - Stage 1 and Stage 2 must be completed successfully
  - Portainer API must be accessible
  - Docker Compose file must exist in tenant directory

Notes:
  - By default, the script waits for Keycloak container to be fully started
  - Use --skip-keycloak-check to bypass Keycloak startup verification
  - Use --validate to check configuration without deploying
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    
    operation_group = parser.add_mutually_exclusive_group()
    operation_group.add_argument(
        '--delete',
        action='store_true',
        help='Delete the Docker stack for the customer'
    )
    operation_group.add_argument(
        '--status',
        action='store_true',
        help='Show status of the Docker stack'
    )
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate the Docker stack configuration'
    )
    
    parser.add_argument(
        '--skip-keycloak-check',
        action='store_true',
        help='Skip Keycloak startup verification after deployment'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    if args.status:
        operation = "status"
    elif args.delete:
        operation = "delete"
    elif args.validate:
        operation = "validate"
    else:
        operation = "deploy"

    try:
        # Create and run Docker stack setup
        setup = TenantDockerStackSetup(args.customer_name)
        setup.skip_keycloak_check = args.skip_keycloak_check
        success = setup.run(operation)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Docker stack deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()