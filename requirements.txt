# LEOS360 Platform - Python Requirements
# =====================================
# 
# This file contains all Python package dependencies required for the
# LEOS360 tenant setup scripts (stage1-4 and setup_tenant_complete.py)
#
# Installation:
#   pip install -r requirements.txt
#
# Python Version: 3.12+

# HTTP Requests and API Communication
# Used by: stage2_tenant_dns.py, stage2_tenant_webproxy.py, stage3_tenant_docker.py,
#          stage4_tenant_keycloak.py, stage4_tenant_lldap.py, stage4_tenant_portal.py
requests>=2.31.0

# PostgreSQL Database Connectivity
# Used by: stage2_tenant_db.py
psycopg2-binary>=2.9.7

# Environment Variable Management
# Used by: stage2_tenant_db.py, stage2_tenant_dns.py, stage3_tenant_docker.py,
#          stage4_tenant_keycloak.py, stage4_tenant_lldap.py, stage4_tenant_portal.py
python-dotenv>=1.0.0

# HTTP Client Library (for SSL handling)
# Used by: stage3_tenant_docker.py, stage4_tenant_lldap.py
urllib3>=2.0.0

# YAML Processing Library
# Used by: stage3_tenant_docker.py (for Docker Compose file validation)
PyYAML>=6.0.1

# Optional: For better SSL certificate handling
# Uncomment if you encounter SSL certificate issues
# certifi>=2023.7.22

# =============================================================================
# MODERN ALTERNATIVES (RECOMMENDED UPGRADES)
# =============================================================================

# Modern PostgreSQL driver (recommended upgrade from psycopg2)
# Migration guide: https://www.psycopg.org/psycopg3/docs/basic/from_pg2.html
# Uncomment to upgrade (requires code changes in stage2_tenant_db.py):
psycopg[binary]>=3.1.0

# Data validation and settings management
# Replaces manual regex validation with structured models
pydantic>=2.5.0

# Advanced retry mechanisms with exponential backoff
# Replaces custom retry decorators with more sophisticated retry logic
tenacity>=8.2.0

# Structured logging (optional upgrade)
# Provides better log formatting and structured output
structlog>=23.2.0

# Modern HTTP client with async support (optional alternative to requests)
# httpx>=0.25.0

# =============================================================================
# DEVELOPMENT DEPENDENCIES (OPTIONAL)
# =============================================================================

# Uncomment for development/testing
# pytest>=7.4.0
# pytest-cov>=4.1.0
# black>=23.7.0
# flake8>=6.0.0
# mypy>=1.7.0
