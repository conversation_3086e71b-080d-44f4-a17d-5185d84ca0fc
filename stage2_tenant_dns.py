#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 2: DNS Configuration
=============================================

This script handles the DNS configuration for a new tenant in the LEOS360 platform.
It creates CNAME records for the customer domain, SSO endpoint, and API endpoint
using PowerDNS API.

Author: LEOS360 Development Team
Version: 3.0
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 must be completed successfully
- PowerDNS API must be accessible
- API credentials must be configured in environment
- DNS zone must exist and be manageable

Usage:
    python3 stage2_tenant_dns.py <customer_name> [options]

Examples:
    python3 stage2_tenant_dns.py example-customer
    python3 stage2_tenant_dns.py example-customer --delete
    python3 stage2_tenant_dns.py example-customer --status
    python3 stage2_tenant_dns.py example-customer --validate
"""

import sys
import time
import argparse
import re
from typing import Tuple, List, Optional, Dict, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# Import common functions
from leos360_common import (
    # Exceptions
    LEOS360Error, ConfigurationError, ValidationError,
    # Logging
    setup_logging,
    # Print functions
    print_header, print_step, print_warning, print_error, print_success,
    # Validation
    validate_customer_name,
    # Environment handling
    load_env_file, get_env_variable,
    # Constants
    TENANTS_BASE_PATH, DEFAULT_API_TIMEOUT, DEFAULT_API_RETRIES,
    DEFAULT_API_BACKOFF_FACTOR, DNS_NAME_PATTERN
)

# =============================================================================
# DNS-SPECIFIC EXCEPTIONS
# =============================================================================

class DNSSetupError(LEOS360Error):
    """Base exception for DNS setup errors."""
    pass

class APIError(DNSSetupError):
    """Raised when API operations fail."""
    pass

class ZoneNotFoundError(DNSSetupError):
    """Raised when DNS zone is not found."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "3.0"
SCRIPT_NAME = "Stage 2: DNS Configuration"

# Load environment variables
load_env_file()

# DNS configuration from environment with validation
API_URL = get_env_variable("POWERDNS_API_URL") or get_env_variable("API_URL")
API_KEY = get_env_variable("POWERDNS_API_KEY") or get_env_variable("API_KEY")
ZONE_NAME = get_env_variable("DNS_ZONE_NAME") or get_env_variable("ZONE_NAME")
TARGET_CNAME = get_env_variable("DNS_TARGET_CNAME", "webproxy01.leos360.cloud.")

# DNS record configuration
DNS_TTL = int(get_env_variable("DNS_TTL", "3600"))
DNS_RECORD_TYPE = "CNAME"

# API configuration (use common defaults)
API_TIMEOUT = DEFAULT_API_TIMEOUT
API_RETRIES = DEFAULT_API_RETRIES
API_BACKOFF_FACTOR = DEFAULT_API_BACKOFF_FACTOR

# Log configuration
BASE_TENANT_PATH = TENANTS_BASE_PATH
LOG_PERMISSIONS = 0o644


# =============================================================================
# DATA CLASSES
# =============================================================================

@dataclass
class DNSRecord:
    """Represents a DNS record."""
    name: str
    type: str
    content: str
    ttl: int = DNS_TTL
    disabled: bool = False

@dataclass
class DNSOperation:
    """Represents a DNS operation result."""
    record_name: str
    operation: str  # 'create', 'update', 'delete', 'skip'
    success: bool
    message: str = ""


# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================

def validate_environment() -> None:
    """
    Validate required environment variables.
    
    Raises:
        ConfigurationError: If required variables are missing or invalid
    """
    missing_vars = []
    
    if not API_URL:
        missing_vars.append("POWERDNS_API_URL or API_URL")
    elif not _is_valid_url(API_URL):
        raise ConfigurationError(f"Invalid API_URL format: {API_URL}")
        
    if not API_KEY:
        missing_vars.append("POWERDNS_API_KEY or API_KEY")
    elif len(API_KEY) < 10:
        raise ConfigurationError("API_KEY appears to be too short (minimum 10 characters)")
        
    if not ZONE_NAME:
        missing_vars.append("DNS_ZONE_NAME or ZONE_NAME")
    elif not re.match(DNS_NAME_PATTERN, ZONE_NAME):
        raise ConfigurationError(f"Invalid ZONE_NAME format: {ZONE_NAME}")
    
    if missing_vars:
        raise ConfigurationError(f"Missing required environment variables: {', '.join(missing_vars)}")

def _is_valid_url(url: str) -> bool:
    """Validate URL format."""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
    except Exception:
        return False


# =============================================================================
# DNS-SPECIFIC UTILITY FUNCTIONS
# =============================================================================


def validate_dns_name(dns_name: str) -> bool:
    """
    Validate DNS name format.
    
    Args:
        dns_name: DNS name to validate
        
    Returns:
        True if valid, False otherwise
    """
    return bool(re.match(DNS_NAME_PATTERN, dns_name))


# =============================================================================
# HTTP SESSION MANAGER
# =============================================================================

class PowerDNSSession:
    """HTTP session manager with retry logic and proper error handling."""
    
    def __init__(self, api_url: str, api_key: str):
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=API_RETRIES,
            backoff_factor=API_BACKOFF_FACTOR,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "PATCH", "PUT"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        session.headers.update({
            "X-API-Key": self.api_key,
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": f"LEOS360-DNS-Setup/{SCRIPT_VERSION}"
        })
        
        return session
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an API request with proper error handling.
        
        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            **kwargs: Additional request arguments
            
        Returns:
            Response object
            
        Raises:
            APIError: If request fails
        """
        url = f"{self.api_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=API_TIMEOUT,
                **kwargs
            )
            response.raise_for_status()
            return response
            
        except requests.exceptions.Timeout:
            raise APIError(f"Request timed out after {API_TIMEOUT} seconds")
        except requests.exceptions.ConnectionError:
            raise APIError(f"Connection failed to {url}")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise APIError("Authentication failed - check API key")
            elif e.response.status_code == 403:
                raise APIError("Access forbidden - insufficient permissions")
            elif e.response.status_code == 404:
                raise APIError(f"Resource not found: {url}")
            elif e.response.status_code == 422:
                raise APIError(f"Invalid data: {e.response.text}")
            else:
                raise APIError(f"HTTP {e.response.status_code}: {e.response.text}")
        except requests.exceptions.RequestException as e:
            raise APIError(f"Request failed: {str(e)}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Close the session."""
        self.session.close()


# =============================================================================
# MAIN DNS CONFIGURATION CLASS
# =============================================================================

class TenantDNSSetup:
    """
    Handles DNS configuration for a new tenant in the LEOS360 platform.

    This class manages the complete DNS setup process including:
    - CNAME record creation for customer domain
    - SSO endpoint DNS configuration  
    - API endpoint DNS configuration
    - PowerDNS zone management and activation
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant DNS setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If environment configuration is invalid
        """
        # Validate environment first
        validate_environment()
        
        # Validate and normalize customer name
        customer_name = customer_name.strip().lower()
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name
        
        # Setup logging
        self.logger = setup_logging(customer_name)
        self.logger.info(f"Initializing DNS setup for: {customer_name}")

        # DNS record configuration
        self.dns_records = self._build_dns_records()
        
        # API configuration
        self.api_url = API_URL
        self.api_key = API_KEY
        self.zone_name = ZONE_NAME
        self.target_cname = TARGET_CNAME
        
        # Validate DNS records
        self._validate_dns_records()

    def _build_dns_records(self) -> List[DNSRecord]:
        """Build list of DNS records for this customer."""
        base_domain = "leos360.cloud"
        records = []
        
        # Main domain
        records.append(DNSRecord(
            name=f"{self.customer_name}.{base_domain}.",
            type=DNS_RECORD_TYPE,
            content=TARGET_CNAME,
            ttl=DNS_TTL
        ))
        
        # SSO endpoint
        records.append(DNSRecord(
            name=f"{self.customer_name}-sso.{base_domain}.",
            type=DNS_RECORD_TYPE,
            content=TARGET_CNAME,
            ttl=DNS_TTL
        ))
        
        # API endpoint
        records.append(DNSRecord(
            name=f"{self.customer_name}-api.{base_domain}.",
            type=DNS_RECORD_TYPE,
            content=TARGET_CNAME,
            ttl=DNS_TTL
        ))
        
        return records

    def _validate_dns_records(self) -> None:
        """
        Validate DNS record names.
        
        Raises:
            ValidationError: If any DNS record name is invalid
        """
        for record in self.dns_records:
            if not validate_dns_name(record.name):
                raise ValidationError(f"Invalid DNS record name: {record.name}")

    # =========================================================================
    # ZONE MANAGEMENT METHODS
    # =========================================================================

    def verify_zone_exists(self, session: PowerDNSSession) -> bool:
        """
        Verify that the DNS zone exists and is accessible.
        
        Args:
            session: PowerDNS API session
            
        Returns:
            True if zone exists and is accessible
            
        Raises:
            ZoneNotFoundError: If zone doesn't exist or isn't accessible
        """
        try:
            response = session.request("GET", f"servers/localhost/zones/{self.zone_name}")
            zone_data = response.json()
            
            if zone_data.get("name") != self.zone_name:
                raise ZoneNotFoundError(f"Zone name mismatch: expected {self.zone_name}")
                
            self.logger.info(f"Zone {self.zone_name} verified successfully")
            return True
            
        except APIError as e:
            if "404" in str(e):
                raise ZoneNotFoundError(f"DNS zone {self.zone_name} not found")
            raise
    
    def get_zone_records(self, session: PowerDNSSession) -> Dict[str, Any]:
        """
        Get all records in the zone.
        
        Args:
            session: PowerDNS API session
            
        Returns:
            Zone data with records
        """
        response = session.request("GET", f"servers/localhost/zones/{self.zone_name}")
        return response.json()

    # =========================================================================
    # DNS RECORD MANAGEMENT METHODS
    # =========================================================================

    def check_record_exists(self, session: PowerDNSSession, record: DNSRecord) -> Tuple[bool, Optional[str]]:
        """
        Check if a DNS record already exists.

        Args:
            session: PowerDNS API session
            record: DNS record to check

        Returns:
            Tuple of (exists, current_content)
        """
        try:
            zone_data = self.get_zone_records(session)
            
            if "rrsets" in zone_data:
                for rrset in zone_data["rrsets"]:
                    if rrset["name"] == record.name and rrset["type"] == record.type:
                        for rec in rrset["records"]:
                            if not rec.get("disabled", False):
                                self.logger.debug(f"Record {record.name} exists with content: {rec['content']}")
                                return True, rec["content"]
            
            return False, None
            
        except Exception as e:
            self.logger.error(f"Error checking record existence: {e}")
            return False, None

    def update_record(self, session: PowerDNSSession, record: DNSRecord) -> DNSOperation:
        """
        Create or update a DNS record.

        Args:
            session: PowerDNS API session
            record: DNS record to create/update

        Returns:
            DNSOperation result
        """
        try:
            # Check if record already exists
            exists, existing_content = self.check_record_exists(session, record)
            
            if exists and existing_content == record.content:
                return DNSOperation(
                    record_name=record.name,
                    operation="skip",
                    success=True,
                    message="Record already exists with correct content"
                )

            # Prepare record data
            rrsets_data = {
                "rrsets": [{
                    "name": record.name,
                    "type": record.type,
                    "ttl": record.ttl,
                    "changetype": "REPLACE",
                    "records": [{
                        "content": record.content,
                        "disabled": record.disabled
                    }]
                }]
            }

            # Execute update
            session.request("PATCH", f"servers/localhost/zones/{self.zone_name}", json=rrsets_data)
            
            operation = "update" if exists else "create"
            self.logger.info(f"Successfully {operation}d record {record.name}")
            
            return DNSOperation(
                record_name=record.name,
                operation=operation,
                success=True,
                message=f"Record successfully {operation}d"
            )

        except Exception as e:
            self.logger.error(f"Failed to update record {record.name}: {e}")
            return DNSOperation(
                record_name=record.name,
                operation="create/update",
                success=False,
                message=str(e)
            )

    def delete_record(self, session: PowerDNSSession, record: DNSRecord) -> DNSOperation:
        """
        Delete a DNS record.

        Args:
            session: PowerDNS API session
            record: DNS record to delete

        Returns:
            DNSOperation result
        """
        try:
            # Check if record exists
            exists, _ = self.check_record_exists(session, record)
            
            if not exists:
                return DNSOperation(
                    record_name=record.name,
                    operation="skip",
                    success=True,
                    message="Record does not exist"
                )

            # Prepare deletion data
            rrsets_data = {
                "rrsets": [{
                    "name": record.name,
                    "type": record.type,
                    "changetype": "DELETE"
                }]
            }

            # Execute deletion
            session.request("PATCH", f"servers/localhost/zones/{self.zone_name}", json=rrsets_data)
            
            self.logger.info(f"Successfully deleted record {record.name}")
            
            return DNSOperation(
                record_name=record.name,
                operation="delete",
                success=True,
                message="Record successfully deleted"
            )

        except Exception as e:
            self.logger.error(f"Failed to delete record {record.name}: {e}")
            return DNSOperation(
                record_name=record.name,
                operation="delete",
                success=False,
                message=str(e)
            )

    # =========================================================================
    # ZONE ACTIVATION METHODS
    # =========================================================================

    def notify_zone(self, session: PowerDNSSession) -> bool:
        """
        Send a NOTIFY request for the zone.

        Args:
            session: PowerDNS API session

        Returns:
            True if successful
        """
        try:
            session.request("PUT", f"servers/localhost/zones/{self.zone_name}/notify")
            self.logger.info(f"Successfully sent NOTIFY for zone {self.zone_name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to notify zone: {e}")
            return False

    def rectify_zone(self, session: PowerDNSSession) -> bool:
        """
        Rectify the zone (corrects DNSSEC records).

        Args:
            session: PowerDNS API session

        Returns:
            True if successful
        """
        try:
            session.request("PUT", f"servers/localhost/zones/{self.zone_name}/rectify")
            self.logger.info(f"Successfully rectified zone {self.zone_name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to rectify zone: {e}")
            return False

    def activate_changes(self, session: PowerDNSSession) -> List[str]:
        """
        Activate changes by rectifying and notifying the zone.

        Args:
            session: PowerDNS API session

        Returns:
            List of successful actions
        """
        success_actions = []

        # Rectify zone
        if self.rectify_zone(session):
            success_actions.append("rectify")

        # Notify slaves
        if self.notify_zone(session):
            success_actions.append("notify")

        # Brief pause for asynchronous processing
        if success_actions:
            time.sleep(2)

        return success_actions

    # =========================================================================
    # STATUS AND VALIDATION METHODS
    # =========================================================================

    def show_status(self) -> bool:
        """Show current status of DNS records."""
        print_step(f"Checking DNS status for customer: {self.customer_name}")
        
        try:
            with PowerDNSSession(self.api_url, self.api_key) as session:
                # Verify zone exists
                self.verify_zone_exists(session)
                
                print(f"\nDNS Status for customer: {self.customer_name}")
                print("=" * 50)
                
                all_exist = True
                for record in self.dns_records:
                    exists, content = self.check_record_exists(session, record)
                    if exists:
                        status = "✓" if content == record.content else "⚠"
                        print(f"{status} {record.name} → {content}")
                        if content != record.content:
                            print(f"  Expected: {record.content}")
                    else:
                        print(f"✗ {record.name} (not found)")
                        all_exist = False
                
                if all_exist:
                    print(f"\n✓ All DNS records for {self.customer_name} are properly configured")
                else:
                    print(f"\n⚠ Some DNS records for {self.customer_name} are missing or incorrect")
                
                return True
                
        except Exception as e:
            print_error(f"Failed to check DNS status: {e}")
            return False

    def validate_records(self) -> bool:
        """Validate that all records are correctly configured."""
        print_step(f"Validating DNS records for customer: {self.customer_name}")
        
        try:
            with PowerDNSSession(self.api_url, self.api_key) as session:
                self.verify_zone_exists(session)
                
                validation_errors = []
                for record in self.dns_records:
                    exists, content = self.check_record_exists(session, record)
                    if not exists:
                        validation_errors.append(f"Record {record.name} does not exist")
                    elif content != record.content:
                        validation_errors.append(f"Record {record.name} has incorrect content: {content} (expected: {record.content})")
                
                if validation_errors:
                    print_error("Validation failed:")
                    for error in validation_errors:
                        print_error(f"  - {error}")
                    return False
                else:
                    print_success(f"All DNS records for {self.customer_name} are valid")
                    return True
                    
        except Exception as e:
            print_error(f"Validation failed: {e}")
            return False

    # =========================================================================
    # MAIN OPERATION METHODS
    # =========================================================================

    def setup_dns_records(self) -> bool:
        """
        Set up DNS records for this customer.

        Returns:
            True if all operations successful
        """
        print_step(f"Setting up DNS records for customer: {self.customer_name}")

        try:
            with PowerDNSSession(self.api_url, self.api_key) as session:
                # Verify zone exists
                self.verify_zone_exists(session)
                
                # Process each record
                operations = []
                any_changes = False
                
                for record in self.dns_records:
                    operation = self.update_record(session, record)
                    operations.append(operation)
                    
                    if operation.success and operation.operation in ['create', 'update']:
                        any_changes = True

                # Check if all operations were successful
                failed_operations = [op for op in operations if not op.success]
                if failed_operations:
                    print_error("Some DNS record operations failed:")
                    for op in failed_operations:
                        print_error(f"  - {op.record_name}: {op.message}")
                    return False

                # Activate changes if any were made
                if any_changes:
                    print_step("Activating DNS changes...")
                    success_actions = self.activate_changes(session)
                    
                    if len(success_actions) >= 2:
                        print_success(f"DNS records for {self.customer_name} successfully configured and activated!")
                        print_success(f"Activated: {', '.join(success_actions)}")
                    else:
                        print_warning(f"DNS records configured but activation incomplete: {', '.join(success_actions)}")
                        return False
                else:
                    print_success(f"All DNS records for {self.customer_name} already correctly configured")

                return True

        except Exception as e:
            print_error(f"DNS setup failed: {e}")
            self.logger.error(f"DNS setup failed: {e}", exc_info=True)
            return False

    def delete_dns_records(self) -> bool:
        """
        Delete DNS records for this customer.

        Returns:
            True if all operations successful
        """
        print_step(f"Deleting DNS records for customer: {self.customer_name}")

        try:
            with PowerDNSSession(self.api_url, self.api_key) as session:
                # Verify zone exists
                self.verify_zone_exists(session)
                
                # Process each record
                operations = []
                any_changes = False
                
                for record in self.dns_records:
                    operation = self.delete_record(session, record)
                    operations.append(operation)
                    
                    if operation.success and operation.operation == 'delete':
                        any_changes = True

                # Check if all operations were successful
                failed_operations = [op for op in operations if not op.success]
                if failed_operations:
                    print_error("Some DNS record deletions failed:")
                    for op in failed_operations:
                        print_error(f"  - {op.record_name}: {op.message}")
                    return False

                # Activate changes if any were made
                if any_changes:
                    print_step("Activating DNS changes...")
                    success_actions = self.activate_changes(session)
                    
                    if len(success_actions) >= 2:
                        print_success(f"DNS records for {self.customer_name} successfully deleted and activated!")
                        print_success(f"Activated: {', '.join(success_actions)}")
                    else:
                        print_warning(f"DNS records deleted but activation incomplete: {', '.join(success_actions)}")
                        return False
                else:
                    print_success(f"No DNS records found for {self.customer_name} to delete")

                return True

        except Exception as e:
            print_error(f"DNS deletion failed: {e}")
            self.logger.error(f"DNS deletion failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str) -> bool:
        """
        Main execution function that orchestrates the DNS operations.

        Args:
            operation: Operation to perform ('setup', 'delete', 'status', 'validate')

        Returns:
            True if operation completed successfully
        """
        print_header(f"LEOS360 DNS Configuration - {self.customer_name}")
        self.logger.info(f"Starting DNS {operation} for {self.customer_name}")

        try:
            if operation == "delete":
                success = self.delete_dns_records()
            elif operation == "status":
                success = self.show_status()
            elif operation == "validate":
                success = self.validate_records()
            else:  # default: setup
                success = self.setup_dns_records()

            if success:
                print_header("OPERATION COMPLETED SUCCESSFULLY")
                print_success(f"DNS {operation} for {self.customer_name} completed successfully!")
                self.logger.info(f"DNS {operation} completed successfully")
            else:
                print_error(f"DNS {operation} failed for {self.customer_name}")
                self.logger.error(f"DNS {operation} failed")

            return success

        except (ConfigurationError, ValidationError, ZoneNotFoundError, APIError) as e:
            print_error(f"DNS {operation} failed: {str(e)}")
            self.logger.error(f"DNS {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during DNS {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during DNS {operation}: {str(e)}", exc_info=True)
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute DNS setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant DNS for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage2_tenant_dns.py example-customer
  python3 stage2_tenant_dns.py example-customer --delete  
  python3 stage2_tenant_dns.py example-customer --status
  python3 stage2_tenant_dns.py example-customer --validate

Requirements:
  - Stage 1 must be completed successfully
  - PowerDNS API must be accessible
  - API credentials must be configured in environment
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    
    operation_group = parser.add_mutually_exclusive_group()
    operation_group.add_argument(
        '--delete',
        action='store_true',
        help='Delete DNS records instead of creating them'
    )
    operation_group.add_argument(
        '--status',
        action='store_true',
        help='Show current status of DNS records'
    )
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate that DNS records are correctly configured'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    if args.delete:
        operation = "delete"
    elif args.status:
        operation = "status"
    elif args.validate:
        operation = "validate"
    else:
        operation = "setup"

    try:
        # Create and run DNS setup
        setup = TenantDNSSetup(args.customer_name)
        success = setup.run(operation)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("DNS setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()