#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 4: LLDAP Setup
========================================

This script handles the LLDAP (Light LDAP) setup for a new tenant in the LEOS360 platform.
It executes bootstrap commands in the LLDAP Docker container via the Portainer API to
initialize the LDAP directory structure and configuration.

Author: LEOS360 Development Team
Version: 2.2
Last Updated: 2025-6-2

Prerequisites:
- Stage 1, 2, and 3 must be completed successfully
- Docker stack must be deployed and running
- LLDAP container must be accessible via Portainer API
- Portainer API credentials must be configured

Usage:
    python3 stage4_tenant_lldap.py <customer_name> [options]

Examples:
    python3 stage4_tenant_lldap.py example-customer
    python3 stage4_tenant_lldap.py example-customer --status
    python3 stage4_tenant_lldap.py example-customer --logs
    python3 stage4_tenant_lldap.py example-customer --exec "ls -la"
    python3 stage4_tenant_lldap.py example-customer --validate
"""

import os
import sys
import json
import time
import argparse
import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import urllib3
from dotenv import dotenv_values

# =============================================================================
# CUSTOM EXCEPTIONS
# =============================================================================

class LLDAPSetupError(Exception):
    """Base exception for LLDAP setup errors."""
    pass

class ConfigurationError(LLDAPSetupError):
    """Raised when configuration is invalid."""
    pass

class ContainerError(LLDAPSetupError):
    """Raised when container operations fail."""
    pass

class CommandError(LLDAPSetupError):
    """Raised when command execution fails."""
    pass

class APIError(LLDAPSetupError):
    """Raised when API operations fail."""
    pass

class ValidationError(LLDAPSetupError):
    """Raised when validation fails."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.2"
SCRIPT_NAME = "Stage 4: LLDAP Setup"

# File paths
DOCKER_CONFIG_PATH = Path("/mnt/storage/docker/.env")
BASE_TENANT_PATH = Path("/mnt/storage/tenants")

# Portainer configuration
DEFAULT_ENDPOINT_ID = "11"
PORTAINER_PORT = 9443
API_TIMEOUT = 60
API_RETRIES = 3
API_BACKOFF_FACTOR = 0.3
CONNECT_TIMEOUT = 15
READ_TIMEOUT = 45

# LLDAP configuration
LLDAP_CONTAINER_SUFFIX = "lldap"
BOOTSTRAP_COMMAND = ["./bootstrap.sh"]
COMMAND_EXECUTION_TIMEOUT = 300  # 5 minutes
LOG_TAIL_LINES = 100

# Container states
RUNNING_STATES = ["running"]
STOPPED_STATES = ["exited", "stopped"]

# Validation patterns
CUSTOMER_NAME_PATTERN = r'^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$'
CONTAINER_NAME_PATTERN = r'^[a-zA-Z0-9][a-zA-Z0-9_.-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$'

# Log configuration
LOG_PERMISSIONS = 0o644


# =============================================================================
# DATA CLASSES
# =============================================================================

@dataclass
class PortainerConfig:
    """Configuration for Portainer API."""
    api_key: str
    host: str
    endpoint_id: str = DEFAULT_ENDPOINT_ID
    port: int = PORTAINER_PORT
    verify_ssl: bool = False

@dataclass
class ContainerInfo:
    """Information about a Docker container."""
    id: str
    name: str
    state: str
    status: str
    image: str
    created: Optional[str] = None
    labels: Dict[str, str] = field(default_factory=dict)
    ports: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class CommandResult:
    """Result of a command execution."""
    success: bool
    exit_code: Optional[int] = None
    output: str = ""
    error: str = ""
    exec_id: Optional[str] = None
    duration: float = 0.0


# =============================================================================
# LOGGING SETUP
# =============================================================================

def setup_logging(customer_name: str) -> logging.Logger:
    """Setup logging configuration."""
    logger = logging.getLogger('lldap_setup')
    logger.setLevel(logging.INFO)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if possible)
    try:
        log_dir = BASE_TENANT_PATH / customer_name / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / "lldap_setup.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Set log file permissions
        os.chmod(log_file, LOG_PERMISSIONS)
    except (OSError, PermissionError) as e:
        print(f"[WARNING] Could not setup file logging: {e}")
    
    return logger


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def print_debug(message: str) -> None:
    """Print a formatted debug message."""
    print(f"[DEBUG] {message}")


def validate_customer_name(customer_name: str) -> bool:
    """Validate customer name format."""
    return bool(re.match(CUSTOMER_NAME_PATTERN, customer_name))


def validate_container_name(container_name: str) -> bool:
    """Validate container name format."""
    return bool(re.match(CONTAINER_NAME_PATTERN, container_name))


def validate_hostname(hostname: str) -> bool:
    """Validate hostname format."""
    try:
        # Check if it's an IP address
        import ipaddress
        ipaddress.ip_address(hostname)
        return True
    except ValueError:
        # Check if it's a valid hostname
        return re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$', hostname) is not None


# =============================================================================
# PORTAINER API SESSION
# =============================================================================

class PortainerAPISession:
    """Secure HTTP session manager for Portainer API with retry logic."""
    
    def __init__(self, config: PortainerConfig):
        self.config = config
        self.base_url = f"https://{config.host}:{config.port}/api"
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=API_RETRIES,
            backoff_factor=API_BACKOFF_FACTOR,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        session.headers.update({
            "X-API-Key": self.config.api_key,
            "Accept": "application/json",
            "User-Agent": f"LEOS360-LLDAP-Setup/{SCRIPT_VERSION}"
        })
        
        return session
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an API request with proper error handling.
        
        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            **kwargs: Additional request arguments
            
        Returns:
            Response object
            
        Raises:
            APIError: If request fails
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        # Set default timeouts
        if 'timeout' not in kwargs:
            kwargs['timeout'] = (CONNECT_TIMEOUT, READ_TIMEOUT)
        
        # Set SSL verification
        if 'verify' not in kwargs:
            kwargs['verify'] = self.config.verify_ssl
        
        try:
            response = self.session.request(method=method, url=url, **kwargs)
            
            # Handle specific HTTP errors
            if response.status_code == 401:
                raise APIError("Authentication failed - check API key")
            elif response.status_code == 403:
                raise APIError("Access forbidden - insufficient permissions")
            elif response.status_code == 404:
                raise APIError(f"Resource not found: {endpoint}")
            elif response.status_code == 422:
                raise APIError(f"Invalid data: {response.text}")
            elif response.status_code >= 500:
                raise APIError(f"Server error: {response.status_code} - {response.text}")
            
            return response
            
        except requests.exceptions.Timeout:
            raise APIError(f"Request timed out after {API_TIMEOUT} seconds")
        except requests.exceptions.ConnectionError:
            raise APIError(f"Connection failed to {url}")
        except requests.exceptions.RequestException as e:
            raise APIError(f"Request failed: {str(e)}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()


# =============================================================================
# CONTAINER MANAGER
# =============================================================================

class ContainerManager:
    """Manager for Docker container operations via Portainer API."""
    
    def __init__(self, session: PortainerAPISession, endpoint_id: str, logger: logging.Logger):
        self.session = session
        self.endpoint_id = endpoint_id
        self.logger = logger
    
    def get_containers(self, all_containers: bool = True) -> List[ContainerInfo]:
        """Get list of containers."""
        try:
            params = {"all": "true"} if all_containers else {}
            response = self.session.request(
                "GET", 
                f"endpoints/{self.endpoint_id}/docker/containers/json",
                params=params
            )
            
            if response.status_code != 200:
                raise APIError(f"Failed to get containers: {response.status_code} - {response.text}")

            containers_data = response.json()
            containers = []
            
            for container_data in containers_data:
                container = ContainerInfo(
                    id=container_data.get("Id", ""),
                    name=self._extract_container_name(container_data.get("Names", [])),
                    state=container_data.get("State", ""),
                    status=container_data.get("Status", ""),
                    image=container_data.get("Image", ""),
                    created=container_data.get("Created"),
                    labels=container_data.get("Labels", {}),
                    ports=container_data.get("Ports", [])
                )
                containers.append(container)
                
            return containers
            
        except Exception as e:
            self.logger.error(f"Failed to get containers: {e}")
            raise ContainerError(f"Failed to get containers: {e}")
    
    def _extract_container_name(self, names: List[str]) -> str:
        """Extract clean container name from names list."""
        if not names:
            return "unknown"
        # Remove leading slash and return first name
        return names[0].lstrip("/")
    
    def find_container_by_name(self, container_name: str) -> Optional[ContainerInfo]:
        """Find container by name."""
        try:
            containers = self.get_containers()
            for container in containers:
                if container.name == container_name:
                    return container
            return None
        except Exception as e:
            self.logger.error(f"Failed to find container {container_name}: {e}")
            return None
    
    def get_container_logs(self, container_id: str, tail_lines: int = LOG_TAIL_LINES) -> str:
        """Get container logs."""
        try:
            params = {
                "stdout": "true",
                "stderr": "true",
                "tail": str(tail_lines),
                "timestamps": "true"
            }
            
            response = self.session.request(
                "GET",
                f"endpoints/{self.endpoint_id}/docker/containers/{container_id}/logs",
                params=params
            )
            
            if response.status_code != 200:
                raise APIError(f"Failed to get logs: {response.status_code} - {response.text}")
            
            return response.text
            
        except Exception as e:
            self.logger.error(f"Failed to get container logs: {e}")
            raise ContainerError(f"Failed to get container logs: {e}")


# =============================================================================
# COMMAND EXECUTOR
# =============================================================================

class ContainerCommandExecutor:
    """Executor for commands in Docker containers via Portainer API."""
    
    def __init__(self, session: PortainerAPISession, endpoint_id: str, logger: logging.Logger):
        self.session = session
        self.endpoint_id = endpoint_id
        self.logger = logger
    
    def execute_command(self, container_id: str, command: List[str], 
                       working_dir: Optional[str] = None,
                       timeout: int = COMMAND_EXECUTION_TIMEOUT) -> CommandResult:
        """
        Execute a command in a container.
        
        Args:
            container_id: ID of the container
            command: Command to execute as list of strings
            working_dir: Working directory for command execution
            timeout: Execution timeout in seconds
            
        Returns:
            CommandResult with execution details
        """
        start_time = time.time()
        
        try:
            # Create exec instance
            exec_id = self._create_exec_instance(container_id, command, working_dir)
            if not exec_id:
                return CommandResult(
                    success=False,
                    error="Failed to create exec instance",
                    duration=time.time() - start_time
                )
            
            # Start exec instance
            if not self._start_exec_instance(exec_id):
                return CommandResult(
                    success=False,
                    exec_id=exec_id,
                    error="Failed to start exec instance",
                    duration=time.time() - start_time
                )
            
            # Wait for completion and get result
            return self._wait_for_completion(exec_id, timeout, start_time)
            
        except Exception as e:
            self.logger.error(f"Command execution failed: {e}")
            return CommandResult(
                success=False,
                error=str(e),
                duration=time.time() - start_time
            )
    
    def _create_exec_instance(self, container_id: str, command: List[str],
                             working_dir: Optional[str] = None) -> Optional[str]:
        """Create an exec instance."""
        payload = {
            "AttachStdin": False,
            "AttachStdout": True,
            "AttachStderr": True,
            "Tty": False,
            "Cmd": command
        }
        
        if working_dir:
            payload["WorkingDir"] = working_dir
        
        try:
            response = self.session.request(
                "POST",
                f"endpoints/{self.endpoint_id}/docker/containers/{container_id}/exec",
                json=payload
            )
            
            if response.status_code != 201:
                self.logger.error(f"Failed to create exec instance: {response.status_code} - {response.text}")
                return None
            
            exec_data = response.json()
            exec_id = exec_data.get("Id")
            self.logger.debug(f"Created exec instance: {exec_id}")
            return exec_id
            
        except Exception as e:
            self.logger.error(f"Failed to create exec instance: {e}")
            return None
    
    def _start_exec_instance(self, exec_id: str) -> bool:
        """Start an exec instance."""
        payload = {
            "Detach": False,
            "Tty": False
        }
        
        try:
            response = self.session.request(
                "POST",
                f"endpoints/{self.endpoint_id}/docker/exec/{exec_id}/start",
                json=payload
            )
            
            if response.status_code != 200:
                self.logger.error(f"Failed to start exec instance: {response.status_code} - {response.text}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start exec instance: {e}")
            return False
    
    def _wait_for_completion(self, exec_id: str, timeout: int, start_time: float) -> CommandResult:
        """Wait for command completion and return result."""
        end_time = start_time + timeout
        
        while time.time() < end_time:
            try:
                # Check exec status
                response = self.session.request(
                    "GET",
                    f"endpoints/{self.endpoint_id}/docker/exec/{exec_id}/json"
                )
                
                if response.status_code != 200:
                    return CommandResult(
                        success=False,
                        exec_id=exec_id,
                        error=f"Failed to inspect exec: {response.status_code}",
                        duration=time.time() - start_time
                    )
                
                exec_info = response.json()
                
                # Check if execution is complete
                if not exec_info.get("Running", True):
                    exit_code = exec_info.get("ExitCode", -1)
                    duration = time.time() - start_time
                    
                    return CommandResult(
                        success=exit_code == 0,
                        exit_code=exit_code,
                        exec_id=exec_id,
                        duration=duration,
                        error="Command failed" if exit_code != 0 else ""
                    )
                
                # Wait a bit before checking again
                time.sleep(1)
                
            except Exception as e:
                return CommandResult(
                    success=False,
                    exec_id=exec_id,
                    error=f"Error waiting for completion: {e}",
                    duration=time.time() - start_time
                )
        
        # Timeout reached
        return CommandResult(
            success=False,
            exec_id=exec_id,
            error=f"Command timed out after {timeout} seconds",
            duration=time.time() - start_time
        )


# =============================================================================
# MAIN LLDAP SETUP CLASS
# =============================================================================

class TenantLLDAPSetup:
    """
    Handles LLDAP setup for a new tenant in the LEOS360 platform.

    This class manages the complete LLDAP setup process including:
    - Portainer API configuration and authentication
    - Docker container discovery and management
    - Command execution in LLDAP containers with output capturing
    - Bootstrap script execution and monitoring
    - Error handling and status reporting
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant LLDAP setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValidationError: If customer_name is invalid
            ConfigurationError: If configuration is invalid
        """
        # Validate and normalize customer name
        customer_name = customer_name.strip().lower()
        if not validate_customer_name(customer_name):
            raise ValidationError(
                "Customer name must start and end with alphanumeric characters, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )
        
        self.customer_name = customer_name
        
        # Setup logging
        self.logger = setup_logging(customer_name)
        self.logger.info(f"Initializing LLDAP setup for: {customer_name}")

        # Container configuration
        self.container_name = f"{customer_name}-{LLDAP_CONTAINER_SUFFIX}"
        
        # Validate container name
        if not validate_container_name(self.container_name):
            raise ValidationError(f"Invalid container name: {self.container_name}")

        # Configuration (will be loaded later)
        self.config: Optional[PortainerConfig] = None
        
        # Managers (will be initialized after configuration)
        self.session: Optional[PortainerAPISession] = None
        self.container_manager: Optional[ContainerManager] = None
        self.command_executor: Optional[ContainerCommandExecutor] = None

    # =========================================================================
    # CONFIGURATION METHODS
    # =========================================================================

    def load_portainer_config(self) -> None:
        """Load Portainer configuration from environment file with validation."""
        if not DOCKER_CONFIG_PATH.exists():
            raise ConfigurationError(f"Docker config file not found: {DOCKER_CONFIG_PATH}")

        self.logger.info(f"Loading Portainer configuration from: {DOCKER_CONFIG_PATH}")
        
        try:
            # Use robust env file parsing
            env_vars = dotenv_values(DOCKER_CONFIG_PATH)
            
            api_key = env_vars.get("PORTAINER_API_KEY")
            host = env_vars.get("PORTAINER_HOST")
            
            if not api_key or not host:
                raise ConfigurationError("PORTAINER_API_KEY or PORTAINER_HOST not defined in config")
            
            # Validate API key
            if len(api_key) < 20:
                raise ConfigurationError("PORTAINER_API_KEY appears to be too short")
            
            # Validate host
            if not validate_hostname(host):
                raise ConfigurationError(f"Invalid PORTAINER_HOST format: {host}")
            
            # Build configuration
            self.config = PortainerConfig(
                api_key=api_key,
                host=host,
                endpoint_id=env_vars.get("ENDPOINT_ID", DEFAULT_ENDPOINT_ID),
                port=int(env_vars.get("PORTAINER_PORT", PORTAINER_PORT)),
                verify_ssl=env_vars.get("PORTAINER_VERIFY_SSL", "false").lower() == "true"
            )

            self.logger.info("Portainer configuration loaded successfully")

        except (ValueError, TypeError) as e:
            raise ConfigurationError(f"Invalid Portainer configuration: {e}")

    def _initialize_managers(self) -> None:
        """Initialize API session and managers."""
        if not self.config:
            raise ConfigurationError("Configuration not loaded")

        # Disable SSL warnings if SSL verification is disabled
        if not self.config.verify_ssl:
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            self.logger.warning("SSL verification is disabled")

        # Initialize API session
        self.session = PortainerAPISession(self.config)
        
        # Test API connectivity
        self._test_api_connectivity()

        # Initialize managers
        self.container_manager = ContainerManager(self.session, self.config.endpoint_id, self.logger)
        self.command_executor = ContainerCommandExecutor(self.session, self.config.endpoint_id, self.logger)

    def _test_api_connectivity(self) -> None:
        """Test Portainer API connectivity."""
        try:
            response = self.session.request("GET", "status")
            if response.status_code == 200:
                self.logger.info("Portainer API connectivity test successful")
            else:
                raise APIError(f"API connectivity test failed: {response.status_code}")
        except Exception as e:
            raise APIError(f"Failed to connect to Portainer API: {e}")

    # =========================================================================
    # CONTAINER OPERATIONS
    # =========================================================================

    def get_container_info(self) -> Optional[ContainerInfo]:
        """Get information about the LLDAP container."""
        try:
            return self.container_manager.find_container_by_name(self.container_name)
        except Exception as e:
            self.logger.error(f"Failed to get container info: {e}")
            return None

    def is_container_running(self) -> bool:
        """Check if the LLDAP container is running."""
        container = self.get_container_info()
        if not container:
            return False
        return container.state in RUNNING_STATES

    def get_container_logs(self, tail_lines: int = LOG_TAIL_LINES) -> Optional[str]:
        """Get container logs."""
        container = self.get_container_info()
        if not container:
            return None
            
        try:
            return self.container_manager.get_container_logs(container.id, tail_lines)
        except Exception as e:
            self.logger.error(f"Failed to get container logs: {e}")
            return None

    # =========================================================================
    # COMMAND EXECUTION METHODS
    # =========================================================================

    def execute_bootstrap(self) -> bool:
        """Execute the bootstrap command in the LLDAP container."""
        print_step(f"Executing bootstrap command in container {self.container_name}")
        
        container = self.get_container_info()
        if not container:
            print_error(f"Container {self.container_name} not found")
            return False

        if not self.is_container_running():
            print_error(f"Container {self.container_name} is not running (state: {container.state})")
            return False

        try:
            result = self.command_executor.execute_command(
                container.id,
                BOOTSTRAP_COMMAND,
                working_dir="/app"
            )
            
            self.logger.info(f"Bootstrap command executed in {result.duration:.2f} seconds")
            
            if result.success:
                print_success(f"Bootstrap command completed successfully")
                if result.output:
                    print(f"Output:\n{result.output}")
                return True
            else:
                print_error(f"Bootstrap command failed with exit code {result.exit_code}")
                if result.error:
                    print_error(f"Error: {result.error}")
                if result.output:
                    print(f"Output:\n{result.output}")
                return False
                
        except Exception as e:
            print_error(f"Failed to execute bootstrap command: {e}")
            self.logger.error(f"Bootstrap execution failed: {e}", exc_info=True)
            return False

    def execute_custom_command(self, command_str: str) -> bool:
        """Execute a custom command in the LLDAP container."""
        print_step(f"Executing custom command in container {self.container_name}: {command_str}")
        
        container = self.get_container_info()
        if not container:
            print_error(f"Container {self.container_name} not found")
            return False

        if not self.is_container_running():
            print_error(f"Container {self.container_name} is not running (state: {container.state})")
            return False

        # Parse command string into list
        command_list = command_str.split() if isinstance(command_str, str) else command_str

        try:
            result = self.command_executor.execute_command(
                container.id,
                command_list,
                working_dir="/app"
            )
            
            self.logger.info(f"Custom command executed in {result.duration:.2f} seconds")
            
            print(f"Command executed in {result.duration:.2f} seconds")
            print(f"Exit code: {result.exit_code}")
            
            if result.output:
                print(f"Output:\n{result.output}")
            
            if result.error and not result.success:
                print_error(f"Error: {result.error}")
            
            return result.success
                
        except Exception as e:
            print_error(f"Failed to execute command: {e}")
            self.logger.error(f"Command execution failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # STATUS AND VALIDATION METHODS
    # =========================================================================

    def show_container_status(self) -> bool:
        """Show detailed status of the LLDAP container."""
        print_step(f"Checking LLDAP container status for customer: {self.customer_name}")

        try:
            container = self.get_container_info()

            print(f"\nLLDAP Container Status for customer: {self.customer_name}")
            print("=" * 60)

            if container:
                print(f"✓ Container: {container.name}")
                print(f"  - ID: {container.id[:12]}...")
                print(f"  - State: {container.state}")
                print(f"  - Status: {container.status}")
                print(f"  - Image: {container.image}")
                print(f"  - Created: {container.created or 'Unknown'}")
                
                # Show ports
                if container.ports:
                    print(f"  - Ports:")
                    for port in container.ports:
                        private_port = port.get('PrivatePort', 'N/A')
                        public_port = port.get('PublicPort', 'N/A')
                        port_type = port.get('Type', 'N/A')
                        print(f"    - {private_port}/{port_type} -> {public_port}")
                
                # Show labels
                if container.labels:
                    print(f"  - Labels:")
                    for key, value in list(container.labels.items())[:5]:  # Show first 5 labels
                        print(f"    - {key}: {value}")
                    if len(container.labels) > 5:
                        print(f"    ... and {len(container.labels) - 5} more")
                
                # Health check
                is_running = self.is_container_running()
                health_symbol = "✓" if is_running else "✗"
                health_status = "Running" if is_running else "Not Running"
                print(f"  - Health: {health_symbol} {health_status}")
                
                return True
            else:
                print(f"✗ Container {self.container_name} not found")
                return False

        except Exception as e:
            print_error(f"Failed to check container status: {e}")
            self.logger.error(f"Status check failed: {e}", exc_info=True)
            return False

    def show_container_logs(self, tail_lines: int = LOG_TAIL_LINES) -> bool:
        """Show container logs."""
        print_step(f"Retrieving logs for container {self.container_name}")

        try:
            logs = self.get_container_logs(tail_lines)
            
            if logs:
                print(f"\nContainer Logs (last {tail_lines} lines):")
                print("=" * 60)
                print(logs)
                return True
            else:
                print_warning("No logs available or container not found")
                return False

        except Exception as e:
            print_error(f"Failed to get container logs: {e}")
            self.logger.error(f"Log retrieval failed: {e}", exc_info=True)
            return False

    def validate_configuration(self) -> bool:
        """Validate the LLDAP configuration and setup."""
        print_step(f"Validating LLDAP configuration for customer: {self.customer_name}")

        validation_errors = []

        try:
            # Test Portainer connectivity
            if not self.config:
                self.load_portainer_config()
                self._initialize_managers()
            
            # Check container exists
            container = self.get_container_info()
            if not container:
                validation_errors.append(f"Container {self.container_name} not found")
            else:
                # Check container state
                if container.state not in RUNNING_STATES:
                    validation_errors.append(f"Container is not running (state: {container.state})")
                
                # Check container image
                if not container.image:
                    validation_errors.append("Container image information missing")
                elif "lldap" not in container.image.lower():
                    validation_errors.append(f"Unexpected container image: {container.image}")

            # Check logs for errors
            try:
                logs = self.get_container_logs(50)
                if logs:
                    # Look for common error patterns
                    error_patterns = ["error", "fatal", "panic", "failed", "exception"]
                    log_lower = logs.lower()
                    found_errors = [pattern for pattern in error_patterns if pattern in log_lower]
                    if found_errors:
                        validation_errors.append(f"Potential errors found in logs: {', '.join(found_errors)}")
            except Exception:
                validation_errors.append("Could not retrieve container logs for validation")

            if validation_errors:
                print_error("Configuration validation failed:")
                for error in validation_errors:
                    print_error(f"  - {error}")
                return False
            else:
                print_success(f"LLDAP configuration for {self.customer_name} is valid")
                return True

        except Exception as e:
            print_error(f"Configuration validation failed: {e}")
            self.logger.error(f"Validation failed: {e}", exc_info=True)
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str, **kwargs) -> bool:
        """
        Main execution function that orchestrates the LLDAP operations.

        Args:
            operation: Operation to perform
            **kwargs: Additional operation-specific arguments

        Returns:
            True if operation completed successfully
        """
        print_header(f"LEOS360 LLDAP Setup - {self.customer_name}")
        self.logger.info(f"Starting LLDAP {operation} for {self.customer_name}")

        try:
            # Load configuration and initialize managers
            self.load_portainer_config()
            self._initialize_managers()

            # Handle different operations
            if operation == "status":
                return self.show_container_status()
            elif operation == "logs":
                tail_lines = kwargs.get("tail_lines", LOG_TAIL_LINES)
                return self.show_container_logs(tail_lines)
            elif operation == "validate":
                return self.validate_configuration()
            elif operation == "exec":
                command = kwargs.get("command")
                if not command:
                    print_error("No command specified for exec operation")
                    return False
                return self.execute_custom_command(command)
            else:  # default: bootstrap
                success = self.execute_bootstrap()

                if success:
                    print_header("OPERATION COMPLETED SUCCESSFULLY")
                    print_success(f"LLDAP bootstrap for {self.customer_name} completed successfully!")
                    self.logger.info("LLDAP bootstrap completed successfully")
                else:
                    print_error("LLDAP bootstrap failed")
                    self.logger.error("LLDAP bootstrap failed")

                return success

        except (ConfigurationError, ValidationError, APIError, ContainerError) as e:
            print_error(f"LLDAP {operation} failed: {str(e)}")
            self.logger.error(f"LLDAP {operation} failed: {str(e)}")
            return False

        except Exception as e:
            print_error(f"Unexpected error during LLDAP {operation}: {str(e)}")
            self.logger.error(f"Unexpected error during LLDAP {operation}: {str(e)}", exc_info=True)
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute LLDAP setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Execute LLDAP operations for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage4_tenant_lldap.py example-customer
  python3 stage4_tenant_lldap.py example-customer --status
  python3 stage4_tenant_lldap.py example-customer --logs
  python3 stage4_tenant_lldap.py example-customer --logs --tail 50
  python3 stage4_tenant_lldap.py example-customer --exec "ls -la /app"
  python3 stage4_tenant_lldap.py example-customer --validate

Requirements:
  - Stage 1, 2, and 3 must be completed successfully
  - Docker stack must be deployed and running
  - LLDAP container must be accessible via Portainer API
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match previous stages)'
    )
    
    operation_group = parser.add_mutually_exclusive_group()
    operation_group.add_argument(
        '--status',
        action='store_true',
        help='Show status of the LLDAP container'
    )
    operation_group.add_argument(
        '--logs',
        action='store_true',
        help='Show container logs'
    )
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate the LLDAP configuration'
    )
    operation_group.add_argument(
        '--exec',
        metavar='COMMAND',
        help='Execute a custom command in the container'
    )
    
    parser.add_argument(
        '--tail',
        type=int,
        default=LOG_TAIL_LINES,
        help=f'Number of log lines to show (default: {LOG_TAIL_LINES})'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation and arguments
    if args.status:
        operation = "status"
        kwargs = {}
    elif args.logs:
        operation = "logs"
        kwargs = {"tail_lines": args.tail}
    elif args.validate:
        operation = "validate"
        kwargs = {}
    elif args.exec:
        operation = "exec"
        kwargs = {"command": args.exec}
    else:
        operation = "bootstrap"
        kwargs = {}

    try:
        # Create and run LLDAP setup
        setup = TenantLLDAPSetup(args.customer_name)
        success = setup.run(operation, **kwargs)

        sys.exit(0 if success else 1)

    except (ValidationError, ConfigurationError) as e:
        print_error(f"Configuration error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("LLDAP setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()