#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 1: Tenant Configuration Setup
======================================================

This script handles the initial configuration setup for a new tenant in the LEOS360 platform.
It creates the directory structure, generates secure passwords, and sets up configuration files
for all required services.

Author: LEOS360 Development Team
Version: 2.2
Last Updated: 2025-6-2

Prerequisites:
- Must be run as root
- Master configuration templates must exist in /mnt/storage/setup
- SSL certificates must be available
- Target tenant directory must not already exist

Usage:
    python3 stage1_tenant_config.py <customer_name>

Example:
    python3 stage1_tenant_config.py example-customer
"""

import os
import sys
import re
import shutil
import argparse
import secrets
import string
import datetime
import logging
from pathlib import Path
from typing import Dict, Set, Optional, Tuple, List

# =============================================================================
# CUSTOM EXCEPTIONS
# =============================================================================

class TenantSetupError(Exception):
    """Base exception for tenant setup errors."""
    pass

class PrerequisiteError(TenantSetupError):
    """Raised when prerequisites are not met."""
    pass

class ResourceAllocationError(TenantSetupError):
    """Raised when resources cannot be allocated."""
    pass

class ConfigurationError(TenantSetupError):
    """Raised when configuration setup fails."""
    pass

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.2"
SCRIPT_NAME = "Stage 1: Tenant Configuration Setup"

# Base paths and configuration
BASE_DOMAIN = "leos360.cloud"
MASTER_BASE_PATH = Path("/mnt/storage/setup")
TENANTS_BASE_PATH = Path("/mnt/storage/tenants")

# Network configuration
EXTERNAL_DB_HOST = "************"
EXTERNAL_DB_PORT = "5432"
IP_PREFIX = "172.16.7."
IP_START = 20
IP_END = 150

# File permissions
DIR_PERMISSIONS = 0o755
FILE_PERMISSIONS = 0o644
SCRIPT_PERMISSIONS = 0o755
SECRET_DIR_PERMISSIONS = 0o750
SECRET_FILE_PERMISSIONS = 0o640

# Password configuration
PASSWORD_LENGTH = 32
PASSWORD_CHARS = string.ascii_letters + string.digits + "!@#$%^&*"

# SSL file extensions
SSL_EXTENSIONS = {'.pem', '.crt', '.key', '.cert', '.ca', '.p12', '.pfx'}


# =============================================================================
# LOGGING SETUP
# =============================================================================

def setup_logging(customer_name: str) -> logging.Logger:
    """Setup logging configuration."""
    logger = logging.getLogger('tenant_setup')
    logger.setLevel(logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    log_dir = TENANTS_BASE_PATH / customer_name / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    file_handler = logging.FileHandler(log_dir / "setup.log")
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


# =============================================================================
# MAIN TENANT CONFIGURATION CLASS
# =============================================================================

class TenantConfigSetup:
    """
    Handles configuration setup for a new tenant in the LEOS360 platform.

    This class manages the complete setup process including:
    - Directory structure creation
    - Password generation and storage
    - Service configuration files
    - SSL certificate setup
    - Environment file creation
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant configuration.

        Args:
            customer_name: Name of the customer/tenant (lowercase alphanumeric with hyphens/underscores)

        Raises:
            ValueError: If customer_name contains invalid characters
        """
        # Improved validation: allow underscores and be more flexible
        if not re.match(r'^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$', customer_name):
            raise ValueError("Customer name must start and end with alphanumeric characters, "
                           "and contain only lowercase letters, numbers, hyphens, and underscores")

        if len(customer_name) < 1 or len(customer_name) > 50:
            raise ValueError("Customer name must be between 1 and 50 characters")

        self.customer_name = customer_name
        
        # Setup logging
        self.logger = setup_logging(customer_name)
        self.logger.info(f"Initializing tenant configuration for: {customer_name}")

        # Base configuration
        self.base_domain = BASE_DOMAIN
        self.master_base = MASTER_BASE_PATH
        self.ssl_base = self.master_base / "ssl"
        self.keycloak_realm = f"{customer_name}.{self.base_domain}"

        # Customer specific configuration
        self.customer_domain = f"{customer_name}.{self.base_domain}"
        self.config_base = TENANTS_BASE_PATH / customer_name

        # Database configuration
        self.external_db_host = EXTERNAL_DB_HOST
        self.external_db_port = EXTERNAL_DB_PORT

        # Service configuration paths
        self.dovecot_config = self.config_base / "dovecot"
        self.lldap_config = self.config_base / "lldap"
        self.keycloak_config = self.config_base / "keycloak"
        self.postfix_config = self.config_base / "postfix"
        self.db_config = self.config_base / "db"
        self.redis_config = self.config_base / "redis"
        self.nextcloud_config = self.config_base / "nextcloud"

        # Runtime variables (will be populated during setup)
        self.customer_ip = ""
        self.redis_db_index = ""
        self.redis_secret = ""
        self.signal_secret = ""

        # Generated passwords (will be populated during setup)
        self._passwords = {}

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def replace_variables(self, content: str, replacements: Dict[str, str]) -> str:
        """
        Efficiently replace variables in content with their values.

        Args:
            content: String containing variables to replace
            replacements: Dictionary of placeholder-value pairs

        Returns:
            Content with all placeholders replaced
        """
        # More efficient replacement using a single pass
        for placeholder, value in replacements.items():
            if placeholder.startswith("${") and placeholder.endswith("}"):
                content = content.replace(placeholder, str(value))
        return content

    def safe_copy_file(self, source: Path, target: Path, permissions: int = FILE_PERMISSIONS) -> bool:
        """
        Safely copy file with proper error handling and permissions.

        Args:
            source: Source file path
            target: Destination file path
            permissions: File permissions (default: FILE_PERMISSIONS)

        Returns:
            True if successful, False otherwise

        Raises:
            ConfigurationError: If copy operation fails critically
        """
        try:
            if not source.exists():
                self.logger.warning(f"Source file {source} not found")
                return False

            target.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source, target)  # copy2 preserves metadata
            os.chmod(target, permissions)
            self.logger.debug(f"Copied {source.name} to {target}")
            return True

        except (IOError, OSError, PermissionError) as e:
            self.logger.error(f"Failed to copy {source} to {target}: {e}")
            raise ConfigurationError(f"Failed to copy configuration file: {e}")

    def ensure_directory(self, directory: Path, permissions: int = DIR_PERMISSIONS) -> Path:
        """
        Ensure directory exists with proper permissions.

        Args:
            directory: Directory path
            permissions: Directory permissions (default: DIR_PERMISSIONS)

        Returns:
            Path to the created directory

        Raises:
            ConfigurationError: If directory creation fails
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
            os.chmod(directory, permissions)
            return directory
        except (OSError, PermissionError) as e:
            self.logger.error(f"Failed to create directory {directory}: {e}")
            raise ConfigurationError(f"Failed to create directory: {e}")

    def generate_secure_password(self, length: int = PASSWORD_LENGTH) -> str:
        """
        Generate a cryptographically secure password.

        Args:
            length: Length of the password to generate

        Returns:
            Secure random password string
        """
        # Use secrets.choice for cryptographically secure random selection
        return ''.join(secrets.choice(PASSWORD_CHARS) for _ in range(length))

    # =========================================================================
    # IP ALLOCATION METHODS
    # =========================================================================

    def _find_tenant_dirs(self, search_path: str) -> Set[str]:
        """
        Find all tenant directories with improved error handling.

        Args:
            search_path: Path to search for tenant directories

        Returns:
            Set of tenant directory paths
        """
        try:
            if not os.path.exists(search_path):
                return set()
            
            return {d for d in os.listdir(search_path)
                   if os.path.isdir(os.path.join(search_path, d)) and not d.startswith('.')}
        except (OSError, PermissionError) as e:
            self.logger.error(f"Error listing directories in {search_path}: {e}")
            return set()

    def _get_used_resources(self, tenant_dirs: Set[str]) -> Tuple[Set[str], Set[int]]:
        """
        Get used IPs and indices from tenant directories with better validation.

        Args:
            tenant_dirs: Set of tenant directory paths

        Returns:
            Tuple of (used_ips, used_indices)
        """
        used_ips = set()
        used_indices = set()

        for tenant_dir in tenant_dirs:
            env_file = TENANTS_BASE_PATH / tenant_dir / ".env"
            if not env_file.exists():
                continue

            try:
                content = env_file.read_text(encoding='utf-8')
                
                # Extract IP
                ip_match = re.search(r'CUSTOMER_IP=([^\s\n]+)', content)
                if ip_match:
                    ip = ip_match.group(1).strip()
                    if self._is_valid_ip(ip):
                        used_ips.add(ip)
                        # Extract last octet for index tracking
                        try:
                            last_octet = int(ip.split('.')[-1])
                            if IP_START <= last_octet <= IP_END:
                                used_indices.add(last_octet)
                        except (ValueError, IndexError):
                            pass

                # Extract Redis DB index
                redis_match = re.search(r'REDIS_DB_INDEX=([^\s\n]+)', content)
                if redis_match:
                    try:
                        db_index = int(redis_match.group(1).strip())
                        if IP_START <= db_index <= IP_END:
                            used_indices.add(db_index)
                    except ValueError:
                        pass

            except (IOError, UnicodeDecodeError) as e:
                self.logger.warning(f"Could not process {env_file}: {e}")

        return used_ips, used_indices

    def _is_valid_ip(self, ip: str) -> bool:
        """
        Validate IP address format.

        Args:
            ip: IP address string

        Returns:
            True if valid IP, False otherwise
        """
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not 0 <= int(part) <= 255:
                    return False
            return True
        except (ValueError, AttributeError):
            return False

    def setup_nextfreeip(self) -> None:
        """
        Get the next free IP address and Redis DB index for the customer.

        Raises:
            ResourceAllocationError: If no available resources found
        """
        self.logger.info("Finding next available IP address and Redis DB index...")

        # Find used resources
        tenant_dirs = self._find_tenant_dirs(str(TENANTS_BASE_PATH))
        used_ips, used_indices = self._get_used_resources(tenant_dirs)

        # Find next available index
        for i in range(IP_START, IP_END + 1):
            if i not in used_indices:
                proposed_ip = f"{IP_PREFIX}{i}"
                # Double-check IP isn't already used
                if proposed_ip not in used_ips:
                    self.customer_ip = proposed_ip
                    self.redis_db_index = str(i)
                    self.logger.info(f"Allocated IP: {self.customer_ip}, Redis DB Index: {self.redis_db_index}")
                    return

        raise ResourceAllocationError("No available IP addresses or Redis DB indices found")

    # =========================================================================
    # PREREQUISITE VALIDATION METHODS
    # =========================================================================

    def check_prerequisites(self) -> None:
        """
        Validate prerequisites and requirements.

        Raises:
            PrerequisiteError: If any prerequisite check fails
        """
        self.logger.info("Checking prerequisites...")

        # Check if running as root
        if os.geteuid() != 0:
            raise PrerequisiteError("This script must be run as root")

        # Check required directories
        if not self.master_base.exists():
            raise PrerequisiteError(f"Master directory {self.master_base} does not exist")

        if not self.ssl_base.exists():
            raise PrerequisiteError(f"SSL directory {self.ssl_base} does not exist")

        # Check if customer directory already exists
        if self.config_base.exists():
            raise PrerequisiteError(f"Customer directory {self.config_base} already exists")

        # Check required template files
        required_files = [
            self.master_base / ".env",
            self.master_base / "db" / "db_setup.sql.template",
            self.master_base / "dovecot" / "dovecot-ldap-userdb.conf",
            self.master_base / "dovecot" / "dovecot-ldap-passdb.conf",
            self.master_base / "postfix" / "ldap" / "virtual_aliases.cf",
            self.master_base / "postfix" / "ldap" / "virtual_domains.cf",
            self.master_base / "docker-compose.yml"
        ]

        missing_files = [str(f) for f in required_files if not f.exists()]

        if missing_files:
            error_msg = "Required template files not found:\n" + "\n".join(f"  - {f}" for f in missing_files)
            raise PrerequisiteError(error_msg)

        self.logger.info("All prerequisites validated successfully")

    # =========================================================================
    # PASSWORD GENERATION METHODS
    # =========================================================================

    def generate_passwords(self) -> None:
        """
        Generate secure passwords and save to credentials file.

        Raises:
            ConfigurationError: If password generation or saving fails
        """
        self.logger.info("Generating secure passwords...")

        try:
            # Generate all required passwords
            password_keys = [
                'db_password', 'nextcloud_admin_password', 'keycloak_admin_password',
                'lldap_jwt_secret', 'lldap_key_seed', 'lldap_admin_pass', 'lldap_ro_pass',
                'signal_secret', 'redis_secret', 'nextcloud_keycloak_client_secret',
                'leos360portal_keycloak_client_secret'
            ]

            for key in password_keys:
                self._passwords[key] = self.generate_secure_password()

            # Create .secrets directory
            secrets_dir = self.config_base / ".secrets"
            self.ensure_directory(secrets_dir, SECRET_DIR_PERMISSIONS)

            # Save passwords to credentials file
            credentials_content = self._build_credentials_content()
            credentials_file = secrets_dir / "credentials.txt"
            
            credentials_file.write_text(credentials_content, encoding='utf-8')
            os.chmod(credentials_file, SECRET_FILE_PERMISSIONS)
            
            self.logger.info("Passwords generated and saved to credentials file")

        except (IOError, OSError) as e:
            raise ConfigurationError(f"Failed to generate or save passwords: {e}")

    def _build_credentials_content(self) -> str:
        """Build credentials file content."""
        return f"""# Generated credentials for {self.customer_name}
# Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Customer IP: {self.customer_ip}
Database Password: {self._passwords['db_password']}
Nextcloud Admin Password: {self._passwords['nextcloud_admin_password']}
Keycloak Admin Password: {self._passwords['keycloak_admin_password']}
LLDAP JWT Secret: {self._passwords['lldap_jwt_secret']}
LLDAP Key Seed: {self._passwords['lldap_key_seed']}
LLDAP Admin Password: {self._passwords['lldap_admin_pass']}
LLDAP RO Password: {self._passwords['lldap_ro_pass']}
Signal Secret: {self._passwords['signal_secret']}
Redis DB Index: {self.redis_db_index}
Redis Password: {self._passwords['redis_secret']}
Nextcloud Keycloak Client Secret: {self._passwords['nextcloud_keycloak_client_secret']}
Leos360 Portal Keycloak Client Secret: {self._passwords['leos360portal_keycloak_client_secret']}
"""

    # =========================================================================
    # DIRECTORY STRUCTURE METHODS
    # =========================================================================

    def create_directory_structure(self) -> None:
        """
        Create directory structure for tenant.

        Raises:
            ConfigurationError: If directory creation fails
        """
        self.logger.info(f"Creating directory structure for {self.customer_name}...")

        try:
            # Directory structure definition
            directory_structure = {
                "nextcloud": ["data", "config", "custom_apps", "templates", "setup", "html"],
                "keycloak": ["data", "config"],
                "lldap": [
                    "data",
                    "config/bootstrap/group-configs",
                    "config/bootstrap/group-schemas",
                    "config/bootstrap/user-configs",
                    "config/bootstrap/user-schemas"
                ],
                "dovecot": ["data/mail", "config/conf.d", "config/ldap", "config/ssl", "config/scripts"],
                "postfix": ["config/ldap", "config/main.cf.d", "data/spool"],
                "redis": ["data"],
                "db": [],
                "logs": [],
                "ssl": []
            }

            # Create all directories
            for service, subdirs in directory_structure.items():
                service_base = self.config_base / service
                self.ensure_directory(service_base)
                
                for subdir in subdirs:
                    self.ensure_directory(service_base / subdir)

            # Set base directory permissions
            os.chmod(self.config_base, DIR_PERMISSIONS)
            self.logger.info("Directory structure created successfully")

        except Exception as e:
            raise ConfigurationError(f"Failed to create directory structure: {e}")

    # =========================================================================
    # ENVIRONMENT FILE METHODS
    # =========================================================================

    def create_env_file(self) -> None:
        """
        Create .env file with proper variable substitutions.

        Raises:
            ConfigurationError: If environment file creation fails
        """
        self.logger.info("Creating environment file...")

        try:
            # Copy master .env template
            env_file = self.config_base / ".env"
            if not self.safe_copy_file(self.master_base / ".env", env_file):
                raise ConfigurationError("Failed to copy master .env template")

            # Read the content
            env_content = env_file.read_text(encoding='utf-8')

            # Define all variable replacements
            replacements = self._build_env_replacements()

            # Replace variables
            env_content = self.replace_variables(env_content, replacements)

            # Write updated content
            env_file.write_text(env_content, encoding='utf-8')
            self.logger.info("Environment file created successfully")

        except (IOError, UnicodeDecodeError) as e:
            raise ConfigurationError(f"Failed to create environment file: {e}")

    def _build_env_replacements(self) -> Dict[str, str]:
        """Build environment variable replacements dictionary."""
        return {
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.base_domain,
            "${CONFIG_BASE}": str(self.config_base),
            "${DB_PASSWORD}": self._passwords['db_password'],
            "${NEXTCLOUD_ADMIN_PASSWORD}": self._passwords['nextcloud_admin_password'],
            "${KEYCLOAK_ADMIN_PASSWORD}": self._passwords['keycloak_admin_password'],
            "${LLDAP_JWT_SECRET}": self._passwords['lldap_jwt_secret'],
            "${LLDAP_KEY_SEED}": self._passwords['lldap_key_seed'],
            "${LLDAP_ADMIN_PASS}": self._passwords['lldap_admin_pass'],
            "${LLDAP_RO_PASS}": self._passwords['lldap_ro_pass'],
            "${EXTERNAL_DB_HOST}": self.external_db_host,
            "${EXTERNAL_DB_PORT}": self.external_db_port,
            "${EXTERNAL_DB_USER}": f"{self.customer_name}_admin",
            "${EXTERNAL_NEXTCLOUD_DB}": f"{self.customer_name}_nextcloud",
            "${EXTERNAL_KEYCLOAK_DB}": f"{self.customer_name}_keycloak",
            "${EXTERNAL_LLDAP_DB}": f"{self.customer_name}_lldap",
            "${CUSTOMER_IP}": self.customer_ip,
            "${SIGNAL_SECRET}": self._passwords['signal_secret'],
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self._passwords['redis_secret'],
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self._passwords['nextcloud_keycloak_client_secret'],
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self._passwords['leos360portal_keycloak_client_secret']
        }

    # =========================================================================
    # SSL SETUP METHODS
    # =========================================================================

    def setup_ssl(self, ssl_dir: Path) -> None:
        """
        Set up SSL certificates with validation.

        Args:
            ssl_dir: Directory where SSL certificates should be copied

        Raises:
            ConfigurationError: If SSL setup fails
        """
        self.logger.info("Setting up SSL certificates...")

        try:
            ssl_files_copied = 0
            for file_path in self.ssl_base.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in SSL_EXTENSIONS:
                    if self.safe_copy_file(file_path, ssl_dir / file_path.name):
                        ssl_files_copied += 1

            if ssl_files_copied == 0:
                self.logger.warning("No SSL certificate files found to copy")
            else:
                self.logger.info(f"Copied {ssl_files_copied} SSL certificate files")

        except Exception as e:
            raise ConfigurationError(f"Failed to setup SSL certificates: {e}")

    # =========================================================================
    # SERVICE CONFIGURATION METHODS
    # =========================================================================

    def _setup_service_config(self, service_name: str,
                            config_files: Dict[str, Tuple[Path, Path, Optional[int]]],
                            replacements: Optional[Dict[str, str]] = None) -> None:
        """
        Base method for setting up service configurations with better error handling.

        Args:
            service_name: Name of the service being configured
            config_files: Dictionary of config files to process
            replacements: Optional variable replacements

        Raises:
            ConfigurationError: If service configuration fails
        """
        self.logger.info(f"Setting up {service_name} configuration...")

        try:
            files_processed = 0
            for description, (source, target, permissions) in config_files.items():
                if not source.exists():
                    self.logger.warning(f"{service_name} source file {source} not found for {description}")
                    continue

                content = source.read_text(encoding='utf-8')
                if replacements:
                    content = self.replace_variables(content, replacements)

                target.parent.mkdir(parents=True, exist_ok=True)
                target.write_text(content, encoding='utf-8')

                if permissions is not None:
                    os.chmod(target, permissions)
                else:
                    os.chmod(target, FILE_PERMISSIONS)

                files_processed += 1

            self.logger.info(f"Processed {files_processed} {service_name} configuration files")

        except (IOError, UnicodeDecodeError, OSError) as e:
            raise ConfigurationError(f"Failed to setup {service_name} configuration: {e}")

    # [Rest of the service setup methods remain largely the same but with better error handling]
    # I'll include a few key ones with improvements:

    def setup_nextcloud(self) -> None:
        """Set up Nextcloud configuration."""
        replacements = {
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self._passwords['redis_secret'],
            "${SIGNAL_SECRET}": self._passwords['signal_secret'],
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.base_domain,
            "${KEYCLOAK_REALM}": self.keycloak_realm,
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self._passwords['nextcloud_keycloak_client_secret'],
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self._passwords['leos360portal_keycloak_client_secret']
        }

        config_files = {
            "setup script": (
                self.master_base / "nextcloud" / "50-setup-nc.sh",
                self.config_base / "nextcloud" / "setup" / "50-setup-nc.sh",
                SCRIPT_PERMISSIONS
            )
        }

        self._setup_service_config("Nextcloud", config_files, replacements)

    def setup_database(self) -> None:
        """Set up database configuration with improved error handling."""
        self.logger.info("Setting up database configuration...")

        try:
            db_setup_file = self.config_base / "db" / f"db_setup_{self.customer_name}.sql"
            self.ensure_directory(self.config_base / "db")

            template_file = self.master_base / "db" / "db_setup.sql.template"

            if not template_file.exists():
                raise ConfigurationError(f"Database template file {template_file} not found")

            sql_content = template_file.read_text(encoding='utf-8')

            # Define database-specific replacements
            db_replacements = {
                "${DB_ADMIN_USER}": f"{self.customer_name}_admin",
                "${DB_NEXTCLOUD}": f"{self.customer_name}_nextcloud",
                "${DB_KEYCLOAK}": f"{self.customer_name}_keycloak",
                "${DB_LLDAP}": f"{self.customer_name}_lldap",
                "${DB_PASSWORD}": self._passwords['db_password'],
                "${CUSTOMER_IP}": self.customer_ip,
            }

            sql_content = self.replace_variables(sql_content, db_replacements)
            db_setup_file.write_text(sql_content, encoding='utf-8')
            os.chmod(db_setup_file, FILE_PERMISSIONS)
            self.logger.info("Database setup script created successfully")

        except (IOError, UnicodeDecodeError, OSError) as e:
            raise ConfigurationError(f"Failed to setup database configuration: {e}")

    def setup_dovecot(self) -> None:
        """
        Set up Dovecot configuration including proper ownership for mail data directory.

        The Dovecot container runs with UID/GID 2000:2000, so the mail data directory
        needs to have the correct ownership for the container to access it.

        Raises:
            ConfigurationError: If Dovecot setup fails
        """
        self.logger.info("Setting up Dovecot configuration...")

        try:
            # Set ownership of mail data directory to 2000:2000 for Dovecot container
            mail_data_dir = self.config_base / "dovecot" / "data" / "mail"

            if not mail_data_dir.exists():
                raise ConfigurationError(f"Dovecot mail data directory {mail_data_dir} does not exist")

            # Change ownership to UID 2000, GID 2000 (dovecot user in container)
            os.chown(mail_data_dir, 2000, 2000)
            self.logger.info(f"Set ownership of {mail_data_dir} to 2000:2000 for Dovecot container")

            # Also set ownership for the parent data directory
            data_dir = self.config_base / "dovecot" / "data"
            os.chown(data_dir, 2000, 2000)
            self.logger.info(f"Set ownership of {data_dir} to 2000:2000 for Dovecot container")

            self.logger.info("Dovecot configuration completed successfully")

        except (OSError, PermissionError) as e:
            raise ConfigurationError(f"Failed to setup Dovecot configuration: {e}")

    # =========================================================================
    # VERIFICATION AND COORDINATION METHODS
    # =========================================================================

    def verify_setup(self) -> None:
        """
        Verify the setup by checking required files and directories.

        Raises:
            ConfigurationError: If verification fails
        """
        self.logger.info("Verifying setup...")

        required_items = [
            self.config_base / ".env",
            self.config_base / "docker-compose.yml",
            self.config_base / "nextcloud",
            self.config_base / "keycloak",
            self.config_base / "lldap",
            self.config_base / "dovecot",
            self.config_base / "postfix",
            self.config_base / ".secrets" / "credentials.txt"
        ]

        missing_items = [str(item) for item in required_items if not item.exists()]

        if missing_items:
            error_msg = "Setup verification failed - missing items:\n" + "\n".join(f"  - {item}" for item in missing_items)
            raise ConfigurationError(error_msg)

        self.logger.info(f"Setup verification completed successfully for {self.customer_name}")

    def cleanup(self) -> None:
        """Enhanced cleanup with better error handling."""
        if self.config_base.exists():
            self.logger.warning(f"Cleaning up {self.config_base} due to error...")
            try:
                shutil.rmtree(self.config_base)
                self.logger.info("Cleanup completed successfully")
            except (OSError, PermissionError) as e:
                self.logger.error(f"Cleanup failed: {e}")

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self) -> bool:
        """
        Main execution function with comprehensive error handling.

        Returns:
            True if setup completed successfully, False otherwise
        """
        print_header(f"LEOS360 Tenant Setup - {self.customer_name}")
        self.logger.info(f"Starting tenant setup for {self.customer_name}...")

        try:
            # Step 1: Validate prerequisites
            self.check_prerequisites()

            # Step 2: Create base directory and allocate resources
            self.config_base.mkdir(parents=True, exist_ok=True)
            self.setup_nextfreeip()

            # Step 3: Generate secure passwords
            self.generate_passwords()

            # Step 4: Create directory structure
            self.create_directory_structure()

            # Step 5: Create environment file
            self.create_env_file()

            # Step 6: Setup SSL
            ssl_dir = self.config_base / "ssl"
            self.ensure_directory(ssl_dir)
            self.setup_ssl(ssl_dir)

            # Step 7: Setup services (simplified for brevity)
            self.setup_nextcloud()
            self.setup_database()
            self.setup_dovecot()
            # Add other service setups here...

            # Step 8: Copy docker-compose.yml
            self.logger.info("Copying Docker Compose configuration...")
            if not self.safe_copy_file(
                self.master_base / "docker-compose.yml",
                self.config_base / "docker-compose.yml"
            ):
                raise ConfigurationError("Failed to copy Docker Compose configuration")

            # Step 9: Verify setup
            self.verify_setup()

            print_header("SETUP COMPLETED SUCCESSFULLY")
            print_success(f"Tenant {self.customer_name} has been configured successfully!")
            print_success(f"Configuration directory: {self.config_base}")
            print_success(f"Customer IP: {self.customer_ip}")
            print_success(f"Redis DB Index: {self.redis_db_index}")

            self.logger.info("Tenant setup completed successfully")
            return True

        except (PrerequisiteError, ResourceAllocationError, ConfigurationError) as e:
            print_error(f"Setup failed: {str(e)}")
            self.logger.error(f"Setup failed: {str(e)}")
            self.cleanup()
            return False

        except Exception as e:
            print_error(f"Unexpected error during setup: {str(e)}")
            self.logger.error(f"Unexpected error during setup: {str(e)}", exc_info=True)
            self.cleanup()
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant configuration for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage1_tenant_config.py example-customer
  python3 stage1_tenant_config.py test-tenant-01
  python3 stage1_tenant_config.py company_name_inc

Requirements:
  - Must be run as root
  - Master templates must exist in /mnt/storage/setup
  - Target tenant directory must not already exist
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (lowercase alphanumeric with hyphens/underscores only)'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    try:
        # Create and run setup
        setup = TenantConfigSetup(args.customer_name)
        success = setup.run()

        sys.exit(0 if success else 1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()